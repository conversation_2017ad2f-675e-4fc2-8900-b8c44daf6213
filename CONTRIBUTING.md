# Contributing

Thank you for considering contributing ! We appreciate your time and effort to help make this project better.

## Before You Start

1. **Search for Existing Issues or Discussions:**
   - Before opening a new issue or discussion, please check if there's already an existing one related to your topic. This helps avoid duplicates and keeps discussions centralized.

2. **Discuss Your Contribution:**
   - If you plan to make a significant change, it's advisable to discuss it in an issue first. This ensures that your contribution aligns with the project's goals and avoids duplicated efforts.

3. **General questions about whisper streaming web:**
   - For general questions about whisper streaming web, use the discussion space on GitHub. This helps in fostering a collaborative environment and encourages knowledge-sharing.

## Opening Issues

If you encounter a problem with diart or want to suggest an improvement, please follow these guidelines when opening an issue:

- **Bug Reports:**
  - Clearly describe the error. **Please indicate the parameters you use, especially the model(s)**
  - Provide a minimal, reproducible example that demonstrates the issue.

- **Feature Requests:**
  - Clearly outline the new feature you are proposing.
  - Explain how it would benefit the project.

## Opening Pull Requests

We welcome and appreciate contributions! To ensure a smooth review process, please follow these guidelines when opening a pull request:

- **Commit Messages:**
  - Write clear and concise commit messages, explaining the purpose of each change.

- **Documentation:**
  - Update documentation when introducing new features or making changes that impact existing functionality.

- **Tests:**
  - If applicable, add or update tests to cover your changes.

- **Discuss Before Major Changes:**
  - If your PR includes significant changes, discuss it in an issue first.

## Thank You

Your contributions make diart better for everyone. Thank you for your time and dedication!