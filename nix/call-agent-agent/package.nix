{
  dream2nix,
  config,
  localFlake,
  ...
}: {
  paths.projectRoot = localFlake;
  paths.projectRootFile = "flake.nix";
  paths.package = "./.";

  imports = [
    dream2nix.modules.dream2nix.WIP-python-pdm
  ];

  deps = {nixpkgs, ...}: {
    python = nixpkgs.python3;
    inherit (nixpkgs) ffmpeg;
  };

  mkDerivation = {
    src = config.paths.projectRoot;
    postInstall = ''
      cp -Tru . $out
      makeWrapper ${config.public.pyEnv}/bin/python $out/bin/agent \
        --add-flags "$out/src/main.py start" \
        --prefix PATH ":" "${config.deps.ffmpeg}/bin"
    '';
  };

  buildPythonPackage = {
    pythonImportsCheck = [
      "agents"
    ];
  };

  pdm.lockfile = config.paths.projectRoot + "/pdm.lock";
  pdm.pyproject = config.paths.projectRoot + "/pyproject.toml";
}
