{withSystem}: {
  lib,
  config,
  self,
  inputs,
  ...
}: {
  imports = [
    inputs.devenv.flakeModule
  ];

  perSystem = {
    config,
    pkgs,
    lib,
    self',
    ...
  }: {
    devenv.shells.default = {
      config,
      lib,
      pkgs,
      ...
    }: {
      imports = [
        inputs.onepunch-infra.devenvModules.onepunch
      ];
      devenv.root = let
        devenvRootFileContent = builtins.readFile inputs.devenv-root.outPath;
      in
        pkgs.lib.mkIf (devenvRootFileContent != "") devenvRootFileContent;
      devenv.tmpdir = let
        devenvTmpdirFileContent = builtins.readFile inputs.devenv-tmpdir.outPath;
      in
        pkgs.lib.mkIf (devenvTmpdirFileContent != "") devenvTmpdirFileContent;

      packages = with pkgs; [
        lego
        alejandra
        age
        inputs.deploy-rs.packages."${system}".deploy-rs
        inputs.nix-update.packages."${system}".nix-update
      ];

      enterShell = ''
        export LEGO_PATH="$DEVENV_STATE/.lego"
      '';
    };
  };
}
