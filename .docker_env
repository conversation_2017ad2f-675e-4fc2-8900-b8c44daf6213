LOG_LEVEL=WARNING
PORT=8000
DEBUG=False
REDIS_PASSWORD=call-agent

TAVILY_API_KEY=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
LIVEKIT_URL=ws://livekit:7880

WEBHOOK_URL=https://03ce-5-34-1-136.ngrok-free.app/v1/livekit/handler

LIVEKIT_API_KEY=APIeyFSC4YEbaWY
LIVEKIT_API_SECRET=7PZRkOqnMrsJirBKT2vngLDQk6zdhf6xeMEIenOxnejC

LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_PUBLIC_KEY=pk-lf-3c85b460-4a43-422a-9b62-131dbb7f16ee
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENABLE_TRACE=False

OPENAI_API_KEY=***********************************************************************************************************************************************
LLM_MODEL=gpt-4o

#Jana-0.2
DEFAULT_METADATA = '{"type": "outbound-campaign-call", "analysis": {"prompt": "call_metadata: {client_name: , agent_name: , call_date: , call_duration: } | feedback_responses: {first_impression: , needs_assessment: , information_quality: , overall_interaction: , next_steps_clarity: } | satisfaction_assessment: {overall_satisfaction: , notable_positives: [], areas_for_improvement: [], completed_questions: true/false} | call_quality_metrics: {client_engagement: , conversation_flow: , questions_answered: all/partial}"}, "llm": {"name": "Albert", "model": "gpt-4o", "language": "eng", "prompt": "A bank that sells credit cards"}, "tts": {"name": "Camilla", "voiceId": "lSDyBIglyLP8FAMvutxO", "provider": "playai", "details": {"provider": "11labs", "similarity_boost": 0.6, "stability": 0.4, "style": 0, "use_speaker_boost": true}}, "context": {"companyId": "62dd9340-4aed-4c08-93a4-bbdef08248b8", "companyName": "local_bank", "conversationId": "4829de4f-bd9d-45ac-acbb-a15898638cef", "campaignId": "02de8981-622f-4a58-a9f2-f68bfc698995", "timezone": "Asia/Dubai", "participant": {"phoneNumber": "+************", "firstName": "John", "lastName": "Doe", "email": "<EMAIL>", "preferredLanguage": "eng", "customerSegment": "premium", "lastContactDate": "2024-03-20", "notes": "Interested in credit card offers"}, "callSchedule": {"mondayStart": "09:00:00", "mondayEnd": "18:00:00", "tuesdayStart": "09:00:00", "tuesdayEnd": "18:00:00", "wednesdayStart": "09:00:00", "wednesdayEnd": "18:00:00", "thursdayStart": "09:00:00", "thursdayEnd": "18:00:00", "fridayStart": "09:00:00", "fridayEnd": "18:00:00", "saturdayStart": null, "saturdayEnd": null, "sundayStart": null, "sundayEnd": null}}}'

# API Credentials
CORE_API_URL=https://api.dev.callevate.ai
CORE_API_LOGIN=callevate
CORE_API_PASSWORD=kfxusskjr

CONVERSATION_API_URL=https://conversation-history.dev.callevate.ai
CONVERSATION_API_LOGIN=callevate
CONVERSATION_API_PASSWORD=3199211d70ddf01cddfa728b7123931133ccfc1ef8f42b126a63305c81333e3f

# STT
STT_PROVIDER=deepgram
STT_URL=ws://livekit:43007

ASSEMBLYAI_API_KEY=********************************
ASSEMBLYAI_MODEL=default

DEEPGRAM_API_KEY=****************************************
DEEPGRAM_MODEL=nova-2

## TTS Providers
VOICE_PROVIDER=playai
ELEVENLABS_API_KEY=***************************************************

TTS_VOICE_ID=21m00Tcm4TlvDq8ikWAM

PLAYHT_USER_ID=kAIhfqsPK1O8QN3Z5yRCxLw7tqD3
PLAYHT_API_KEY=ak-c850d8ef67da457ca13495e1f0061fad

ENVIRONMENT=Dev.Docker

SENTRY_DSN=https://<EMAIL>/46
SENTRY_ENVIRONMENT=local

GOOGLE_APPLICATION_CREDENTIALS=speech-api-credentials.json

# Result Handler Configuration
RESULT_HANDLER_ENABLED=true
USE_LIVERICHY_HANDLER=false
SUMMARY_MODEL=gpt-3.5-turbo
ANALYSIS_MODEL=gpt-3.5-turbo
MAX_HISTORY_LENGTH=100
SAVE_INTERMEDIATE_RESULTS=true

#
REDIS_PASSWORD=call-agent
GPU_MODE=False