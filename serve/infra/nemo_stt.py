import io
import logging
import wave
from dataclasses import dataclass
from typing import Any, Optional

import aiohttp
import numpy as np
import torch
import torchaudio
from livekit.agents import stt, APIConnectOptions, DEFAULT_API_CONNECT_OPTIONS
from livekit.rtc.audio_frame import AudioFrame

from serve.asr.nemo.model_server import NemoModelServer  # Updated import
from serve.asr.nemo.nemo_stt import NemoModel
from .speech_stream_ws import SpeechStream

logger = logging.getLogger(__name__)


@dataclass
class NemoSTTOptions:
    model: NemoModel = NemoModel.CONFORMER_CTC
    device: str = "auto"
    sample_rate: int = 16000
    num_channels: int = 1
    language: str = "eng"


class NemoSTT(stt.STT):
    def __init__(self, *, opts: NemoSTTOptions | None = None):
        super().__init__(
            capabilities=stt.STTCapabilities(streaming=True, interim_results=True)
        )
        self._opts = opts or NemoSTTOptions()
        if self._opts.language == "ara":
            self._opts.model = NemoModel.QUARTZNET_ARABIC
        self._model_server: Optional[NemoModelServer] = None
        self._content, self._wave = self._new_wave()
        self._session: Optional[aiohttp.ClientSession] = None
        self._base_url = "ws://localhost:8000"  # Update this with your NeMo server URL

    def _ensure_session(self) -> aiohttp.ClientSession:
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession()
        return self._session

    async def aclose(self):
        """Close the ClientSession when it's no longer needed."""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None

    async def _load(self):
        logger.debug(f"Loading NeMo model for {self._opts.language}...")
        self._model_server = NemoModelServer(
            model_name=self._opts.model.value,
            device=self._opts.device
        )
        await self._model_server.load_model()
        logger.debug(f"Loaded NeMo model: {self._opts.model.value}")

    @classmethod
    async def load(cls, opts: NemoSTTOptions | None = None) -> "NemoSTT":
        instance = cls(opts=opts)
        await instance._load()
        return instance

    def _new_wave(self):
        content = io.BytesIO()
        ww = wave.open(content, "wb")
        ww.setsampwidth(2)
        ww.setnchannels(self._opts.num_channels)
        ww.setframerate(self._opts.sample_rate)
        return (content, ww)

    def stream(
            self,
            *,
            language: str | None = None,
            conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS,
    ) -> SpeechStream:
        stream = SpeechStream(
            stt=self,
            opts=self._opts,
            conn_options=conn_options,
            http_session=self._ensure_session(),
            base_url=self._base_url,
        )
        return stream

    async def _recognize_impl(
            self,
            frame: AudioFrame,
            *,
            language: str | None = None,
            conn_options: Any = None,
    ) -> stt.SpeechEvent:
        if not self._model_server:
            logger.error("NeMo model not available")
            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(text="", language=self._opts.language, confidence=0.0)
                ],
            )

        try:
            # Convert AudioFrame to numpy array and normalize
            audio_data = frame.data.tobytes()
            audio_float = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0

            # Convert numpy array to torch tensor
            audio_tensor = torch.from_numpy(audio_float).unsqueeze(0)

            # Resample if needed
            if frame.sample_rate != self._opts.sample_rate:
                resampler = torchaudio.transforms.Resample(
                    orig_freq=frame.sample_rate,
                    new_freq=self._opts.sample_rate
                )
                audio_tensor = resampler(audio_tensor)

            # Remove extra channel dimension if present
            if audio_tensor.ndim == 3 and audio_tensor.shape[1] == 1:
                audio_tensor = audio_tensor.squeeze(1)

            # Move tensor to model device
            audio_tensor = audio_tensor.to(self._model_server.device)

            # Transcribe using model server
            transcription = await self._model_server.transcribe(audio_tensor)

            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(
                        text=transcription,
                        language=self._opts.language,
                        confidence=1.0,
                    )
                ],
            )
        except Exception as e:
            logger.error(f"NeMo transcription error: {e}")
            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(text="", language=self._opts.language, confidence=0.0)
                ],
            )
