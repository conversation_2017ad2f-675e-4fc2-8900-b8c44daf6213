import asyncio
import json
import logging
from typing import Any, Optional

from livekit import rtc
from livekit.agents import stt, utils

logger = logging.getLogger(__name__)


class WhisperStream(stt.SpeechStream):
    _CLOSE_MSG: str = json.dumps({"type": "CloseStream"})
    _FINALIZE_MSG: str = json.dumps({"type": "Finalize"})

    def __init__(
            self,
            *,
            stt: stt.STT,
            opts: Any,
            conn_options: Any,
            host: str = 'localhost',
            port: int = 43007,
    ) -> None:
        super().__init__(
            stt=stt, conn_options=conn_options, sample_rate=opts.sample_rate
        )
        self._opts = opts
        self._host = host
        self._port = port
        self._speaking = False
        self._reconnect_event = asyncio.Event()
        self._request_id: Optional[str] = None
        self._last_end = None

    async def _run(self) -> None:
        reader, writer = await asyncio.open_connection(self._host, self._port)

        async def send_task():
            audio_buffer = utils.audio.AudioByteStream(
                sample_rate=self._opts.sample_rate,
                num_channels=1,
                samples_per_channel=self._opts.sample_rate // 20,  # 50ms chunks
            )

            try:
                async for data in self._input_ch:
                    if isinstance(data, rtc.AudioFrame):
                        # Convert audio frame to bytes and send
                        audio_bytes = data.data.tobytes()
                        writer.write(audio_bytes)
                        await writer.drain()

                    elif isinstance(data, self._FlushSentinel):
                        writer.write(self._FINALIZE_MSG.encode())
                        await writer.drain()
            except Exception as e:
                logger.error(f"Error in send task: {e}")
            finally:
                writer.write(self._CLOSE_MSG.encode())
                await writer.drain()

        async def recv_task():
            try:
                while True:
                    data = await reader.readline()
                    if not data:
                        break

                    try:
                        decoded_data = data.decode().strip()
                        logger.debug(f"Received data: {decoded_data}")

                        # Parse timestamp and text from Whisper server format
                        parts = decoded_data.split(" ", 3)
                        logger.debug(f"Split parts: {parts}")

                        if len(parts) == 3:
                            # Format: "start_time end_time transcript"
                            start_time, end_time, text = parts
                            is_final = False  # Default to interim for 3-part messages
                            logger.debug("3-part message detected, treating as interim")
                        elif len(parts) == 4:
                            # Format: "start_time end_time transcript is_final"
                            start_time, end_time, text, is_final = parts
                            is_final = is_final.lower() == 'true'
                            logger.debug(f"4-part message detected, is_final: {is_final}")
                        else:
                            logger.warning(f"Unexpected format, got {len(parts)} parts: {parts}")
                            continue

                        start_time = float(start_time) / 1000  # Convert ms to seconds
                        end_time = float(end_time) / 1000

                        logger.debug(f"Processed times: start={start_time:.3f}, end={end_time:.3f}")
                        logger.debug(f"Text content: {text}")

                        # Create speech event
                        alternatives = [
                            stt.SpeechData(
                                language=self._opts.language,
                                text=text,
                                confidence=1.0,
                                start_time=start_time,
                                end_time=end_time,
                            )
                        ]

                        if not self._speaking:
                            self._speaking = True
                            logger.debug("Starting speech")
                            self._event_ch.send_nowait(
                                stt.SpeechEvent(type=stt.SpeechEventType.START_OF_SPEECH)
                            )

                        event_type = (
                            stt.SpeechEventType.FINAL_TRANSCRIPT if is_final
                            else stt.SpeechEventType.INTERIM_TRANSCRIPT
                        )

                        logger.debug(f"Sending event type: {event_type}")
                        self._event_ch.send_nowait(
                            stt.SpeechEvent(
                                type=event_type,
                                alternatives=alternatives,
                            )
                        )

                    except Exception as e:
                        logger.error(f"Failed to process server message: {e}", exc_info=True)

            except Exception as e:
                logger.error(f"Error in receive task: {e}", exc_info=True)
            finally:
                if self._speaking:
                    self._speaking = False
                    logger.debug("Ending speech")
                    self._event_ch.send_nowait(
                        stt.SpeechEvent(type=stt.SpeechEventType.END_OF_SPEECH)
                    )

        tasks = [
            asyncio.create_task(send_task()),
            asyncio.create_task(recv_task()),
        ]

        try:
            await asyncio.gather(*tasks)
        finally:
            for task in tasks:
                task.cancel()
            writer.close()
            await writer.wait_closed()

    async def reconnect(self) -> None:
        """Trigger a reconnection to the Whisper server"""
        self._reconnect_event.set()
