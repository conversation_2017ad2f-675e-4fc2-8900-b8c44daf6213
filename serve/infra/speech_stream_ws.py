import asyncio
import json
import logging
from typing import Any, Optional

import aiohttp
from livekit import rtc
from livekit.agents import stt, utils, APIStatusError

from serve.infra.tools.audio_stream_filters import AudioEnergyFilter

logger = logging.getLogger(__name__)


class SpeechStream(stt.SpeechStream):
    _CLOSE_MSG: str = json.dumps({"type": "CloseStream"})
    _FINALIZE_MSG: str = json.dumps({"type": "Finalize"})

    def __init__(
            self,
            *,
            stt: stt.STT,
            opts: any,
            conn_options: any,
            http_session: aiohttp.ClientSession,
            base_url: str,
    ) -> None:
        super().__init__(
            stt=stt, conn_options=conn_options, sample_rate=opts.sample_rate
        )
        self._api_key = ""
        self._opts = opts
        self._session = http_session
        self._base_url = base_url
        self._speaking = False
        self._reconnect_event = asyncio.Event()
        self._request_id: Optional[str] = None
        self._audio_energy_filter = AudioEnergyFilter()

    def _check_energy_state(self, frame: rtc.AudioFrame) -> AudioEnergyFilter.State:
        if self._audio_energy_filter:
            return self._audio_energy_filter.update(frame)
        return AudioEnergyFilter.State.SPEAKING

    async def _run(self) -> None:
        closing_ws = False

        async def send_task(ws: aiohttp.ClientWebSocketResponse):
            nonlocal closing_ws
            audio_bstream = utils.audio.AudioByteStream(
                sample_rate=self._opts.sample_rate,
                num_channels=1,  # Whisper expects mono
                samples_per_channel=self._opts.sample_rate // 20,  # 50ms chunks
            )

            has_ended = False
            last_frame: Optional[rtc.AudioFrame] = None
            async for data in self._input_ch:
                frames: list[rtc.AudioFrame] = []
                if isinstance(data, rtc.AudioFrame):
                    state = self._check_energy_state(data)
                    if state in (
                            AudioEnergyFilter.State.START,
                            AudioEnergyFilter.State.SPEAKING,
                    ):
                        if last_frame:
                            frames.extend(
                                audio_bstream.write(last_frame.data.tobytes())
                            )
                            last_frame = None
                        frames.extend(audio_bstream.write(data.data.tobytes()))
                    elif state == AudioEnergyFilter.State.END:
                        # no need to buffer as we have cooldown period
                        frames = audio_bstream.flush()
                        has_ended = True
                    elif state == AudioEnergyFilter.State.SILENCE:
                        # buffer the last silence frame, since it could contain beginning of speech
                        # TODO: improve accuracy by using a ring buffer with longer window
                        last_frame = data
                    elif isinstance(data, self._FlushSentinel):
                        frames = audio_bstream.flush()
                        has_ended = True

                for frame in frames:
                    await ws.send_bytes(frame.data.tobytes())
                    if has_ended:
                        await ws.send_str(self._FINALIZE_MSG)
                        has_ended = False
            closing_ws = True
            await ws.send_str(self._CLOSE_MSG)

        async def recv_task(ws: aiohttp.ClientWebSocketResponse):
            nonlocal closing_ws
            while True:
                msg = await ws.receive()
                if msg.type in (
                        aiohttp.WSMsgType.CLOSED,
                        aiohttp.WSMsgType.CLOSE,
                        aiohttp.WSMsgType.CLOSING,
                ):
                    if closing_ws:
                        return
                    raise APIStatusError(
                        message="whisper connection closed unexpectedly"
                    )

                if msg.type != aiohttp.WSMsgType.TEXT:
                    logger.warning("unexpected whisper message type %s", msg.type)
                    continue

                try:
                    self._process_stream_event(json.loads(msg.data))
                except Exception:
                    logger.exception("failed to process whisper message")

        ws: aiohttp.ClientWebSocketResponse | None = None
        while True:
            try:
                ws = await self._connect_ws()
                tasks = [
                    asyncio.create_task(send_task(ws)),
                    asyncio.create_task(recv_task(ws)),
                ]
                wait_reconnect_task = asyncio.create_task(self._reconnect_event.wait())
                try:
                    done, _ = await asyncio.wait(
                        [asyncio.gather(*tasks), wait_reconnect_task],
                        return_when=asyncio.FIRST_COMPLETED,
                    )

                    for task in done:
                        if task != wait_reconnect_task:
                            task.result()

                    if wait_reconnect_task not in done:
                        break

                    self._reconnect_event.clear()
                finally:
                    await utils.aio.gracefully_cancel(*tasks, wait_reconnect_task)
            finally:
                if ws is not None:
                    await ws.close()

    async def _connect_ws(self) -> aiohttp.ClientWebSocketResponse:
        config: dict[str, Any] = {
            "model": str(self._opts.model),
            "language": self._opts.language,
            "sample_rate": self._opts.sample_rate,
        }
        if self._opts.keywords:
            config["keywords"] = self._opts.keywords

        ws = await asyncio.wait_for(
            self._session.ws_connect(
                f"{self._base_url}/asr",
                headers={"Authorization": f"Bearer {self._api_key}"},
            ),
            self._conn_options.timeout,
        )
        return ws

    def _process_stream_event(self, data: dict) -> None:
        if data["type"] == "speech_start":
            if not self._speaking:
                self._speaking = True
                self._event_ch.send_nowait(
                    stt.SpeechEvent(type=stt.SpeechEventType.START_OF_SPEECH)
                )

        elif data["type"] == "transcription":
            self._request_id = data.get("request_id")
            is_final = data.get("is_final", False)

            alternatives = [
                stt.SpeechData(
                    language=self._opts.language,
                    text=data["text"],
                    confidence=data.get("confidence", 1.0),
                    start_time=data.get("start_time", 0),
                    end_time=data.get("end_time", 0),
                )
            ]

            event_type = (
                stt.SpeechEventType.FINAL_TRANSCRIPT
                if is_final
                else stt.SpeechEventType.INTERIM_TRANSCRIPT
            )

            self._event_ch.send_nowait(
                stt.SpeechEvent(
                    type=event_type,
                    request_id=self._request_id,
                    alternatives=alternatives,
                )
            )

        elif data["type"] == "speech_end":
            if self._speaking:
                self._speaking = False
                self._event_ch.send_nowait(
                    stt.SpeechEvent(type=stt.SpeechEventType.END_OF_SPEECH)
                )
