import asyncio
import logging
from typing import Op<PERSON>, <PERSON><PERSON>, List

import numpy as np

try:
    from mlx_whisper.transcribe import ModelHolder, transcribe
    import mlx.core as mx
except ModuleNotFoundError as e:
    raise Exception(f"To use MLX Whisper, install mlx-whisper: {e}")

try:
    from serve.asr.mlx.log import logger
except ImportError:
    logger = logging.getLogger("asr.mlx")


class MLXModelServer:
    """Server for handling MLX Whisper model operations optimized for Apple Silicon"""

    LARGE_V3 = "mlx-community/whisper-large-v3"
    MEDIUM = "mlx-community/whisper-medium"
    SMALL = "mlx-community/whisper-small"
    BASE = "mlx-community/whisper-base"
    TINY = "mlx-community/whisper-tiny"

    def __init__(
            self,
            model_name: str = MEDIUM,
            dtype: str = "float32",
            no_speech_threshold: float = 0.4,
            language_prob_threshold: float = 0.9
    ):
        """
        Initialize MLX Whisper model server
        
        Args:
            model_name: MLX-compatible Whisper model name/path
            dtype: Data type for model computation ('float16' or 'float32')
            no_speech_threshold: Threshold for filtering out non-speech segments
            language_prob_threshold: Threshold for language detection confidence
        """
        self.model_name = model_name
        self._dtype = mx.float16 if dtype == "float16" else mx.float32
        self._model = None
        self.no_speech_threshold = no_speech_threshold
        self.language_prob_threshold = language_prob_threshold

    async def load_model(self):
        """Load the MLX Whisper model asynchronously"""
        logger.debug(f"Loading MLX Whisper model: {self.model_name}")
        try:
            # MLX Whisper uses a static model holder
            await asyncio.to_thread(
                ModelHolder.get_model,
                self.model_name,
                self._dtype
            )
            self._model = transcribe
            logger.debug(f"Loaded MLX Whisper model: {self.model_name}")
            return self._model
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise

    async def transcribe(
            self,
            audio: np.ndarray,
            language: Optional[str] = None
    ) -> Tuple[str, Optional[str], Optional[float]]:
        """
        Transcribe audio using the loaded model
        
        Args:
            audio: Audio array as numpy array
            language: Optional language code for transcription
            
        Returns:
            Tuple containing:
            - transcribed text
            - detected language (if confidence above threshold)
            - language detection confidence
        """
        if not self._model:
            raise RuntimeError("Model not loaded. Call load_model() first.")

        try:
            result = await asyncio.to_thread(
                self._model,
                audio,
                language=language
            )

            # Extract language info from the result
            detected_lang = result.get('language')
            lang_prob = 1.0 if detected_lang else 0.0  # MLX Whisper doesn't provide probability

            # Combine text from valid segments
            text = " ".join(
                segment['text']
                for segment in result.get('segments', [])
                if segment.get('no_speech_prob', 0) < self.no_speech_threshold
            )

            logger.debug(f"Transcription result: {text}")
            return text, detected_lang, lang_prob

        except Exception as e:
            logger.error(f"Error during transcription: {str(e)}")
            raise

    async def transcribe_segments(
            self,
            audio: np.ndarray,
            language: Optional[str] = None
    ) -> List[dict]:
        """
        Transcribe audio and return detailed segment information
        
        Args:
            audio: Audio array as numpy array
            language: Optional language code for transcription
            
        Returns:
            List of segments with timing and confidence information
        """
        if not self._model:
            raise RuntimeError("Model not loaded. Call load_model() first.")

        try:
            result = await asyncio.to_thread(
                self._model,
                audio,
                language=language
            )

            return [
                {
                    "text": segment['text'],
                    "start": segment['start'],
                    "end": segment['end'],
                    "no_speech_prob": segment.get('no_speech_prob', 0),
                    "words": segment.get('words', None)
                }
                for segment in result.get('segments', [])
                if segment.get('no_speech_prob', 0) < self.no_speech_threshold
            ]

        except Exception as e:
            logger.error(f"Error during segment transcription: {str(e)}")
            raise


def main():
    """Example usage of MLXModelServer"""
    import soundfile as sf

    async def run_example():
        # Initialize server with MLX-compatible medium model
        server = MLXModelServer(
            model_name=MLXModelServer.MEDIUM,  # "mlx-community/whisper-medium"
            dtype="float16"
        )

        # Load model
        await server.load_model()

        # Load audio file
        audio_file = "../samples_jfk.wav"  # "../samples_sana.wav"
        audio, sample_rate = sf.read(audio_file)

        # Run transcription with Arabic language
        text, lang, confidence = await server.transcribe(audio)
        print("\nTranscription result:")
        print("-" * 50)
        print(f"Text: {text}")
        print(f"Detected language: {lang} (confidence: {confidence:.2f})")
        print("-" * 50)

        # Get detailed segments
        segments = await server.transcribe_segments(audio)
        print("\nDetailed segments:")
        for seg in segments:
            print(f"[{seg['start']:.2f}s - {seg['end']:.2f}s]: {seg['text']}")

    asyncio.run(run_example())


if __name__ == "__main__":
    main()
