import asyncio
import logging
from typing import Op<PERSON>, <PERSON><PERSON>, List

import logger
import numpy as np
import torch

try:
    from faster_whisper import WhisperModel
except ModuleNotFoundError as e:
    logger.error(f"Exception: {e}")
    logger.error(
        "In order to use Whisper, you need to `pip install pipecat-ai[whisper]`."
    )
    raise Exception(f"Missing module: {e}")

# Replace relative import with direct import
try:
    from serve.asr.whisp.log import logger
except ImportError:
    logger = logging.getLogger("asr.whisper")


class WhisperModelServer:
    """Server for handling Whisper model operations"""

    LARGE_V3_TURBO = "large-v3"
    MEDIUM = "medium"
    SMALL = "small"
    BASE = "base"
    TINY = "tiny"

    def __init__(
            self,
            model_name: str = SMALL,
            device: str = "auto",
            compute_type: str = "default",
            no_speech_prob_threshold: float = 0.4,
            language_prob_threshold: float = 0.9
    ):
        """
        Initialize Whisper model server
        Args:
            model_name: Whisper model size/name
            device: Device to run model on ('auto', 'cpu', 'cuda')
            compute_type: Compute type for the model
            no_speech_prob_threshold: Threshold for filtering out non-speech segments
            language_prob_threshold: Threshold for language detection confidence
        """
        self.model_name = model_name
        self._device = device
        self._compute_type = compute_type
        self._model: Optional[WhisperModel] = None
        self.no_speech_prob_threshold = no_speech_prob_threshold
        self.language_prob_threshold = language_prob_threshold

    async def load_model(self) -> WhisperModel:
        """Load the Whisper model asynchronously"""
        logger.debug(f"Loading Whisper model: {self.model_name}")
        try:
            self._model = await asyncio.to_thread(
                WhisperModel,
                self.model_name,
                device=self._device,
                compute_type=self._compute_type
            )
            logger.debug(f"Loaded Whisper model: {self.model_name} on device: {self._device}")
            return self._model
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise

    async def transcribe(
            self,
            audio: np.ndarray,
            language: Optional[str] = None
    ) -> Tuple[str, Optional[str], Optional[float]]:
        """
        Transcribe audio using the loaded model
        
        Args:
            audio: Audio array as numpy array
            language: Optional language code for transcription
            
        Returns:
            Tuple containing:
            - transcribed text
            - detected language (if confidence above threshold)
            - language detection confidence
        """
        if not self._model:
            raise RuntimeError("Model not loaded. Call load_model() first.")

        try:
            segments, info = await asyncio.to_thread(
                self._model.transcribe,
                audio,
                language=language
            )

            # Handle language detection
            detected_lang, lang_prob = None, None
            if info.language_probability > self.language_prob_threshold:
                detected_lang, lang_prob = info.language, info.language_probability

            # Combine text from valid segments
            text = " ".join(
                segment.text
                for segment in segments
                if segment.no_speech_prob < self.no_speech_prob_threshold
            )

            logger.debug(f"Transcription result: {text}")
            return text, detected_lang, lang_prob

        except Exception as e:
            logger.error(f"Error during transcription: {str(e)}")
            raise

    async def transcribe_segments(
            self,
            audio: np.ndarray,
            language: Optional[str] = None
    ) -> List[dict]:
        """
        Transcribe audio and return detailed segment information
        
        Args:
            audio: Audio array as numpy array
            language: Optional language code for transcription
            
        Returns:
            List of segments with timing and confidence information
        """
        if not self._model:
            raise RuntimeError("Model not loaded. Call load_model() first.")

        try:
            segments, info = await asyncio.to_thread(
                self._model.transcribe,
                audio,
                language=language
            )

            return [
                {
                    "text": segment.text,
                    "start": segment.start,
                    "end": segment.end,
                    "no_speech_prob": segment.no_speech_prob,
                    "words": segment.words
                }
                for segment in segments
                if segment.no_speech_prob < self.no_speech_prob_threshold
            ]

        except Exception as e:
            logger.error(f"Error during segment transcription: {str(e)}")
            raise


def main():
    """Example usage of WhisperModelServer"""
    import soundfile as sf

    async def run_example():
        # Initialize server with Arabic model from HuggingFace
        server = WhisperModelServer(
            model_name="./whisper-medium-ar-ct2",  # Path to converted model
            device="cuda" if torch.cuda.is_available() else "cpu",
            compute_type="float16" if torch.cuda.is_available() else "float32"
        )

        # Load model
        await server.load_model()

        # Load audio file
        audio_file = "../samples_sana.wav"
        audio, sample_rate = sf.read(audio_file)

        # Run transcription (specify Arabic as the language)
        text, lang, confidence = await server.transcribe(audio, language="ar")
        print("\nTranscription result:")
        print("-" * 50)
        print(f"Text: {text}")
        print(f"Detected language: {lang} (confidence: {confidence:.2f})")
        print("-" * 50)

        # Get detailed segments
        segments = await server.transcribe_segments(audio)
        print("\nDetailed segments:")
        for seg in segments:
            print(f"[{seg['start']:.2f}s - {seg['end']:.2f}s]: {seg['text']}")

    asyncio.run(run_example())


if __name__ == "__main__":
    main()
