import asyncio
import logging
from typing import Optional

import nemo.collections.asr as nemo_asr
import torch

# Replace relative import with direct import
try:
    from serve.asr.nemo.log import logger
except ImportError:
    # Fallback if package imports don't work
    logger = logging.getLogger("asr.nemo")


class NemoModelServer:
    device: str = "cpu"
    ARABIC_MODEL = "./models/quartznet_15x5_ara.nemo"  # Local Arabic model
    MULTILINGUAL_MODEL = "nvidia/stt_en_conformer_ctc_large"  # NGC model name

    def __init__(self, model_name: str = MULTILINGUAL_MODEL, device: str = "cpu"):
        """
        Initialize NeMo model server
        Args:
            model_name: Either ARABIC_MODEL for Arabic or MULTILINGUAL_MODEL for multi-language support
            device: Device to run model on ('cpu' or 'cuda')
        """
        self.model_name = model_name
        self._model: Optional[nemo_asr.models.ASRModel] = None
        self._device = device

    async def load_model(self):
        """Load the NeMo model asynchronously"""
        logger.debug(f"Loading NeMo model: {self.model_name}")
        try:
            if self.model_name.endswith('.nemo'):
                # For local .nemo files (like Arabic model)
                self._model = await asyncio.to_thread(
                    nemo_asr.models.ASRModel.restore_from,
                    restore_path=self.model_name
                )
            else:
                # For models from NGC/Hugging Face
                self._model = await asyncio.to_thread(
                    nemo_asr.models.ASRModel.from_pretrained,
                    model_name=self.model_name,
                    map_location=self._device
                )
            
            # Move model to specified device
            self._model = self._model.to(self.device)
            logger.debug(f"Loaded NeMo model: {self.model_name} on device: {self.device}")
            return self._model
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise

    def transcribe_file(self, audio_file: str) -> str:
        """Directly transcribe an audio file without async"""
        if not self._model:
            raise RuntimeError("Model not loaded. Call load_model() first.")

        try:
            # Use NeMo's built-in transcribe method which handles audio file loading
            transcript = self._model.transcribe([audio_file])[0]
            return transcript

        except Exception as e:
            logger.error(f"Error during file transcription: {str(e)}")
            raise

    async def transcribe(self, audio_tensor: torch.Tensor) -> str:
        """Transcribe audio using the loaded model"""
        if not self._model:
            raise RuntimeError("Model not loaded. Call load_model() first.")

        try:
            # Ensure audio tensor is in the correct shape (batch, time)
            if audio_tensor.dim() == 1:
                # Single channel audio -> add batch dimension
                audio_tensor = audio_tensor.unsqueeze(0)
            elif audio_tensor.dim() == 3 and audio_tensor.shape[1] == 1:
                # Shape is (batch, channel, time) -> squeeze out channel dimension
                audio_tensor = audio_tensor.squeeze(1)

            # Move tensor to the same device as the model
            audio_tensor = audio_tensor.to(self.device)

            logger.debug(f"Audio tensor shape after preprocessing: {audio_tensor.shape}")

            # Run transcription in a thread pool
            result = await asyncio.to_thread(
                self._model.transcribe,
                audio_tensor
            )

            logger.debug(f"Transcription result: {result}")
            return result[0] if result else ""
        except Exception as e:
            logger.error(f"Error during transcription: {str(e)}")
            raise

    @property
    def device(self) -> torch.device:
        """Get the device where the model is loaded"""
        if not self._model:
            raise RuntimeError("Model not loaded")
        return next(self._model.parameters()).device


def main():
    """Example usage of direct transcription"""
    audio_file = "../samples_sana.wav"
    
    # Use CUDA if available
    device = "cuda" if torch.cuda.is_available() else "cpu"

    # Initialize and load model - choose either Arabic or Multilingual
    server = NemoModelServer(model_name=NemoModelServer.ARABIC_MODEL, device=device)
    # Or for multilingual:
    # server = NemoModelServer(model_name=NemoModelServer.MULTILINGUAL_MODEL, device=device)

    # Load model synchronously using asyncio
    asyncio.run(server.load_model())

    # Transcribe file
    try:
        transcript = server.transcribe_file(audio_file)
        print(f"\nTranscription result:")
        print("-" * 50)
        print(transcript)
        print("-" * 50)
    except Exception as e:
        print(f"Error: {str(e)}")


if __name__ == "__main__":
    main()
