import asyncio
from enum import Enum

import soundfile as sf
import torch

from serve.asr.nemo.log import logger
from serve.asr.nemo.model_server import NemoModelServer


class NemoModel(str, Enum):
    FASTCONFORMER_TRANSDUCER = "stt_en_fastconformer_transducer_large"
    CONFORMER_CTC = "stt_en_conformer_ctc_large"
    FASTCONFORMER_CTC = "stt_en_fastconformer_ctc_large"  # Adding new model
    CONFORMER_TRANSDUCER = "stt_en_conformer_transducer_large"
    QUARTZNET_ARABIC = "livekit-plugins-nemo/models/quartznet_15x5_ara.nemo"
    PARA_MULTI = "Parakeet-CTC-XXL-1.1B"


def load_audio(file_path: str, sample_rate: int = 16000) -> torch.Tensor:
    """Load audio file and convert to torch tensor with shape (batch, time)"""
    audio, sr = sf.read(file_path)
    if sr != sample_rate:
        raise ValueError(f"Audio must be sampled at {sample_rate}Hz")

    # Convert to mono if stereo
    if len(audio.shape) > 1:
        raise ValueError("Audio must be mono-channel")

    # Convert to torch tensor with shape (batch, time)
    audio_tensor = torch.FloatTensor(audio)
    if audio_tensor.dim() == 1:
        # Add batch dimension if not present
        audio_tensor = audio_tensor.unsqueeze(0)

    # Ensure shape is (batch, time)
    if audio_tensor.dim() == 3:
        audio_tensor = audio_tensor.squeeze(1)

    logger.debug(f"Audio tensor shape: {audio_tensor.shape}")
    return audio_tensor


async def test_transcription(audio_path: str):
    """Test transcription with a given audio file"""
    try:
        # Initialize and load model
        server = NemoModelServer(NemoModel.FASTCONFORMER_TRANSDUCER.value)
        await server.load_model()

        # Load and process audio
        audio_tensor = load_audio(audio_path)

        # Transcribe
        print("\nTranscribing audio...")
        transcription = await server.transcribe(audio_tensor)
        print("\nTranscription result:")
        print("-" * 50)
        print(f"Text: {transcription}")
        print("-" * 50)

    except Exception as e:
        logger.error(f"Error during transcription: {str(e)}")
        raise


def main():
    """Main function to run the test"""
    audio_path = "../samples_jfk.wav"  # Replace with your audio path
    asyncio.run(test_transcription(audio_path))


if __name__ == "__main__":
    main()
