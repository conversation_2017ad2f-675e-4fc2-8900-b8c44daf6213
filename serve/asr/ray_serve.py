from typing import Dict, Any

import ray
import torch
from fastapi import FastAPI
from pydantic import BaseModel
from ray import serve

from serve.asr.nemo.log import logger
from serve.asr.nemo.model_server import NemoModelServer  # Updated import
from serve.asr.nemo.nemo_serve import NemoModel


class AudioRequest(BaseModel):
    """Request model for audio data"""
    audio_tensor: list  # Will be converted to torch tensor
    sample_rate: int = 16000
    model_name: str = NemoModel.CONFORMER_CTC.value


class TranscriptionResponse(BaseModel):
    """Response model for transcription"""
    text: str
    model_name: str


@serve.deployment(
    ray_actor_options={"num_gpus": 1.0},
    max_concurrent_queries=4
)
class NemoServeDeployment:
    """Ray Serve deployment for NemoModelServer"""

    def __init__(self):
        # Initialize model cache
        self.model_servers: Dict[str, NemoModelServer] = {}

    async def ensure_model_loaded(self, model_name: str) -> NemoModelServer:
        """Ensure model is loaded, loading it if necessary"""
        if model_name not in self.model_servers:
            logger.info(f"Loading model: {model_name}")
            model_server = NemoModelServer(model_name=model_name, device="cuda")
            await model_server.load_model()
            self.model_servers[model_name] = model_server
            logger.info(f"Model {model_name} loaded successfully")
        return self.model_servers[model_name]

    async def __call__(self, request: Dict[str, Any]) -> Dict[str, str]:
        """Handle incoming requests"""
        try:
            # Get or load the requested model
            model_server = await self.ensure_model_loaded(request["model_name"])

            # Convert list to tensor
            audio_data = torch.tensor(request["audio_tensor"])
            if audio_data.dim() == 1:
                audio_data = audio_data.unsqueeze(0)
            audio_data = audio_data.to(model_server.device)

            # Get transcription
            transcription = await model_server.transcribe(audio_data)

            return {
                "text": transcription,
                "model_name": model_server.model_name
            }
        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            raise


app = FastAPI()


@app.on_event("startup")
async def startup_event():
    """Initialize Ray and deploy the model"""
    ray.init(address="auto", namespace="nemo_serve")
    serve.start(detached=True)
    NemoServeDeployment.deploy()


@app.post("/transcribe", response_model=TranscriptionResponse)
async def transcribe(request: AudioRequest) -> TranscriptionResponse:
    """Endpoint for transcription requests"""
    handle = serve.get_deployment("NemoServeDeployment").get_handle()
    result = await handle.remote({
        "audio_tensor": request.audio_tensor,
        "sample_rate": request.sample_rate,
        "model_name": request.model_name
    })
    return TranscriptionResponse(**result)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
