from datetime import datetime
from zoneinfo import ZoneInfo

from langfuse import Lang<PERSON>

from app.config import get_config
from log.sentry_decorators import sentry_span

_langfuse_instance = None  # Singleton instance holder

@sentry_span(op="get_langfuse", description="get langfuse")
def get_langfuse():
    global _langfuse_instance
    if _langfuse_instance is None:
        config = get_config()
        _langfuse_instance = Langfuse(
            public_key=config.langfuse.public_key,
            secret_key=config.langfuse.secret_key,
            host=config.langfuse.host,
            enabled=config.langfuse.enable_trace
        )
    return _langfuse_instance

def get_now():
    return datetime.now(ZoneInfo('UTC')).strftime('%Y-%m-%dT%H:%M') + 'Z'

def get_now_s():
    return datetime.now(ZoneInfo('UTC')).strftime('%Y-%m-%dT%H:%M:%S') + 'Z'
