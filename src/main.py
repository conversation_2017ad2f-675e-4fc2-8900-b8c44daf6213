import asyncio
import logging
from contextvars import ContextVar

import sentry_sdk
from livekit.agents import AutoSubscribe, JobContext, WorkerOptions, cli, JobProcess, RoomInputOptions, metrics, \
    MetricsCollectedEvent
from livekit.plugins import noise_cancellation
from livekit.rtc import RemoteTrackPublication, RemoteParticipant

from app.config import get_config
from app.sentry_config import initialize_sentry, capture_errors
from log.sentry_decorators import sentry_transaction, sentry_span

conversation_id_var = ContextVar('conversation_id',default="")

usage_collector = metrics.UsageCollector()

# Initialize configuration and log
config = get_config()
logging.basicConfig(
    level=config.app.log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # This ensures output to console
        logging.FileHandler('app.log')  # This will also save logs to a file
    ]
)
logger = logging.getLogger(__name__)
# logger.propagate = False

initialize_sentry(config)

from conv.conv_mgr import ConvManager

def prewarm(job_process: JobProcess) -> None:
    # vad_profile = get_vad_profile("phone_call")
    # job_process.userdata["vad"] = silero.VAD.load(**vad_profile.to_dict())
    pass


async def get_or_wait_for_metadata(ctx: JobContext, conversation_manager: ConvManager):
    # Create a future that will be resolved when metadata is updated
    future = asyncio.get_event_loop().create_future()
    if len(ctx.room.metadata) > 0:
        future.set_result(ctx.room.metadata)
        return future.result()

    @ctx.room.on("room_metadata_changed")
    def room_metadata_changed(old_metadata, metadata):
        logger.debug(f"Room metadata changed: {metadata}")
        if metadata:
            if not future.done():
                future.set_result(metadata)

    try:
        await asyncio.wait_for(future, timeout=1000.0)  # 10 second timeout
        return future.result()  # Return the metadata
    except asyncio.TimeoutError:
        logger.warning("Timed out waiting for metadata to be updated")
        return ctx.room.metadata  # Return current metadata even if timeout occurred


@sentry_span(op="room", description="wait callee")
async def wait_for_participant(ctx, conversation_manager: ConvManager):
    # Create a future that will be resolved when participant is fully available
    future = asyncio.get_event_loop().create_future()

    participant_obj = await ctx.wait_for_participant()
    # Check if participant already has tracks
    if len(participant_obj.track_publications) > 0:
        conversation_manager.participant_is_joined.set()
        if not future.done():
            future.set_result(participant_obj)
    else:
        # Event handler for track publications
        @ctx.room.on("track_published")
        @sentry_span(op="room.track_published", description="callee added track")
        def track_published(publication: RemoteTrackPublication, participant: RemoteParticipant):
            logger.info(f"{participant.identity} published track: {publication.name}")
            conversation_manager.participant_is_joined.set()
            if not future.done():
                future.set_result(participant_obj)


    @sentry_span(op="participant.attributes_changed", description="callee changed state")
    @ctx.room.on("participant_attributes_changed")
    def participant_attributes_changed(changed_attributes, participant):
        logger.info(f"{participant.identity} attributes changed {changed_attributes}")
        call_status_changed_to = changed_attributes.get('sip.callStatus', '')
        if call_status_changed_to == 'active':
            conversation_manager.participant_is_joined.set()
            logger.info("callStatus: active")
            # Check if we can resolve the future
            if participant_obj and len(participant_obj.track_publications) > 0 and not future.done():
                future.set_result(participant_obj)
        elif call_status_changed_to == 'hangup':
            conversation_manager.set_end_conversation()
            logger.info("callStatus: hangup. ending conversation")
            if not future.done():
                future.set_exception(Exception("Call hungup before participant was fully available"))
        else:
            logger.info(f"callStatus: {call_status_changed_to} (no action taken)")

    try:
        # Wait for the future to be resolved with a timeout
        await asyncio.wait_for(future, timeout=160.0)  # 30 second timeout
        participant = future.result()
        logger.info(
            f"Participant fully available: {participant} conv:{conversation_manager.conv_meta.context.conversationId}")
        return participant
    except asyncio.TimeoutError:
        logger.warning("Timed out waiting for participant to be fully available")
        # Return the participant anyway, even if not fully ready
        if participant_obj:
            logger.info(f"Returning participant despite timeout: {participant_obj}")
            return participant_obj
        raise Exception("No participant available after timeout")

@capture_errors
@sentry_transaction("conversation","entry point")
async def entrypoint(ctx: JobContext) -> None:
    scope = sentry_sdk.get_isolation_scope()
    logger.info(f"Room Name: {ctx.room.name}")

    @sentry_span(op="room.shutdown", description="shutdown")
    async def shutdown_callback(obj=None):
        try:
            if conversation_manager is not None:
                if conversation_manager.conversation_ended:
                    logger.info("Conversation already ended. Skipping shutdown.")
                    return
                conversation_manager.set_end_conversation()
                await ctx.room.disconnect()
        except Exception as e:
            logger.error(f"Error shutting down conversation: {str(e)}")
            sentry_sdk.capture_exception(e)

        logger.info(f"Shutdown callback execution: {obj}")

    ctx.add_shutdown_callback(shutdown_callback)

    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    try:
        conversation_manager = ConvManager(ctx)
        is_inbound_call = ctx.room.name.startswith("inbound-call")
        logger.info(f"is_inbound_call: {is_inbound_call}")
        metadata = await get_or_wait_for_metadata(ctx, conversation_manager)
        conversation_manager.update_metadata(metadata)
    except Exception as e:
        logger.error(f"Failed to initialize ConvManager: {str(e)}")
        logger.error(f"Current room metadata: {ctx.room.metadata!r}")
        raise

    await run_conversation(ctx, conversation_manager, wait_for_participant)


@sentry_span(op="conversation", description="run conversation")
async def run_conversation(ctx: JobContext, conversation_manager: ConvManager, wait_for_participant_fn):
    # Start the conversation
    jana = await conversation_manager.start_conversation()

    participant = await wait_for_participant_fn(ctx, conversation_manager)
    await conversation_manager.session.start(room=ctx.room, agent=jana, room_input_options=RoomInputOptions(
        noise_cancellation=noise_cancellation.BVCTelephony(),
    ))

    @conversation_manager.session.on("metrics_collected")
    def _on_metrics_collected(ev: MetricsCollectedEvent):
        usage_collector.collect(ev.metrics)

    async def log_usage():
        summary = usage_collector.get_summary()
        logger.warning(f"Usage: {summary}")

    ctx.add_shutdown_callback(log_usage)
    await conversation_manager.say_welcome()


def main() -> None:
    worker_options = WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm,
                                   initialize_process_timeout=120,
                                   )
    try:
        logger.info("Starting application")
        cli.run_app(worker_options)
    except asyncio.CancelledError:
        logger.info("Long-running task was cancelled")
    except Exception as e:
        logger.error(f"Error in main application: {str(e)}", exc_info=True)
        sentry_sdk.capture_exception(e)  # Capture unexpected exceptions
    finally:
        logger.info("Shutting down the application.")

# Entry point
if __name__ == "__main__":
    main()