import json
import logging
from typing import Dict, Any

from langfuse.decorators import observe
from livekit.agents.llm import ChatContext, LLMStream, ChatMessage
from livekit.plugins import openai

from app.config import Config
from conv.conv_svc import ConvSvc
from log.sentry_decorators import sentry_span

# Define sentiment labels as constants
POSITIVE = "positive"
NEUTRAL = "neutral"
NEGATIVE = "negative"

_logger = logging.getLogger(__name__)
_logger.propagate = False


class SentimentAnalyzer:
    @sentry_span(op="Agent.sentiment_analyzer", description="Agent.sentiment_analyzer")
    def __init__(self, config: Config, core_api_client: ConvSvc):
        self.config = config
        self.core_api_client = core_api_client
        self.llm = openai.LLM(api_key=config.openai.api_key, model=config.openai.sentiment_analysis_model)
        self.messages = []
        self.new_message_count = 0
        self.system_prompt = '''
        Analyze the sentiment of the given text. Provide the output as a single, valid JSON object.
        This JSON object must have a field "dominant_sentiment" which itself is an object with two fields:
        1. "label": A string, either "positive", "neutral", or "negative".
        2. "score": A float, representing the confidence in the dominant sentiment, between 0.0 and 1.0.

        The JSON object must also have a field "all_scores" which is an object containing three fields:
        1. "positive": A float score between 0.0 and 1.0.
        2. "neutral": A float score between 0.0 and 1.0.
        3. "negative": A float score between 0.0 and 1.0.
        The sum of "positive", "neutral", and "negative" scores in "all_scores" must be 1.0.

        Example of the exact output format:
        {
            "dominant_sentiment": { "label": "positive", "score": 0.8 },
            "all_scores": {
                "positive": 0.8,
                "neutral": 0.15,
                "negative": 0.05
            }
        }
        Reply ONLY with this JSON object. Do not include any other text, explanations, markdown, or backticks.
        '''

    @sentry_span(op="Agent.process_sentiment_analysis", description="Agent.process_sentiment_analysis")
    async def process_sentiment_analysis(self, conversation_id: str, message: str) -> object:
        self.messages.append(message)
        self.new_message_count += 1  # Increment the counter

        # Ensure only the latest three messages are kept
        if len(self.messages) > 2:
            self.messages = self.messages[-2:]

        # Trigger analysis when a new message forms a pair of two messages
        if self.new_message_count >= 1 and len(self.messages) == 2:
            last_two_messages = " ".join(self.messages)
            llm_response_text = await self.analyze_sentiment(last_two_messages)
            processed_result = await self.process_sentiment_result(llm_response_text)
            await self._handle_sentiment_analysis(conversation_id, processed_result)
            self.new_message_count = 0  # Reset the counter

    @sentry_span(op="Agent.process_sentiment_result", description="Agent.process_sentiment_result")
    async def process_sentiment_result(self, llm_response_text: str) -> Dict[str, Any]:
        try:
            sentiment_data = json.loads(llm_response_text)

            if not isinstance(sentiment_data, dict) or \
                    "dominant_sentiment" not in sentiment_data or \
                    not isinstance(sentiment_data["dominant_sentiment"], dict) or \
                    "label" not in sentiment_data["dominant_sentiment"] or \
                    "score" not in sentiment_data["dominant_sentiment"]:
                _logger.error(f"Malformed JSON structure in sentiment analysis result: {llm_response_text}")
                return {"sentiment_label": NEUTRAL, "sentiment_score": 0.0, "all_scores": None}

            dominant_sentiment = sentiment_data["dominant_sentiment"]
            label = dominant_sentiment["label"]
            score = dominant_sentiment["score"]

            if label not in [POSITIVE, NEUTRAL, NEGATIVE]:
                _logger.warning(
                    f"Invalid sentiment label '{label}' received. Defaulting to neutral. Input: {llm_response_text}")
                label = NEUTRAL
                score = 0.0

            try:
                score = float(score)
                score = max(min(score, 1.0), 0.0)
            except (ValueError, TypeError):
                _logger.warning(
                    f"Invalid score format '{score}' for label '{label}'. Defaulting to 0.0. Input: {llm_response_text}")
                score = 0.0
                label = NEUTRAL  # If score is bad, label might also be untrustworthy

            _logger.info(
                f"Sentiment analysis processed: Label: {label}, Score: {score}, All: {sentiment_data.get('all_scores')}")

            return {
                "sentiment_label": label,
                "sentiment_score": score,
                "all_scores": sentiment_data.get("all_scores")
            }
        except json.JSONDecodeError:
            _logger.error(f"Failed to parse sentiment analysis JSON result: {llm_response_text}")
            return {"sentiment_label": NEUTRAL, "sentiment_score": 0.0, "all_scores": None}
        except Exception as e:
            _logger.error(f"Unexpected error during sentiment result processing: {str(e)} - Input: {llm_response_text}")
            return {"sentiment_label": NEUTRAL, "sentiment_score": 0.0, "all_scores": None}

    @observe
    @sentry_span(op="func_call", description="Agent.sentiment_analysis")
    async def analyze_sentiment(self, message: str) -> str:
        messages = [
            ChatMessage(content=[self.system_prompt], role="system"),
            ChatMessage(content=[message], role="user"),
        ]
        ctx = ChatContext(items=messages)

        # Initiate the chat and get the LLMStream
        llm_stream: LLMStream = self.llm.chat(chat_ctx=ctx)
        response_parts = []

        # Asynchronously iterate over the stream of ChatChunks
        async for chunk in llm_stream:
            if chunk.delta and chunk.delta.content:
                response_parts.append(chunk.delta.content)

        # Combine all parts into the final response string
        response = ''.join(response_parts)

        return response

    @sentry_span(op="_handle_sentiment_analysis", description="Agent._handle_sentiment_analysis")
    async def _handle_sentiment_analysis(self, conversation_id: str, sentiment_result: Dict[str, Any]) -> None:
        try:
            sentiment_scores = [{
                "sentiment": sentiment_result["sentiment_label"],
                "sentimentScore": sentiment_result["sentiment_score"]
            }]
            await self.core_api_client.add_sentiment(conversation_id, sentiment_scores)
            self.last_sentiment_analysis = sentiment_result
        except KeyError:
            _logger.error(f"Malformed sentiment_result dictionary for Core API: {sentiment_result}")
        except Exception as e:
            _logger.error(f"Error sending sentiment analysis to Core API: {str(e)}")
