import time
from collections import deque

from .turn_feature_detector import TurnFeatureDetector


class RepeatedGreetingsDetector(TurnFeatureDetector):
	def __init__(self, greet_window_sec=2.0, greet_max_repeats=2):
		self.greet_window_sec = greet_window_sec
		self.greet_max_repeats = greet_max_repeats
		self._recent_greets = deque()
		self.greet_set = {"hello", "hey", "hi", "greetings", "good morning", "good afternoon", "good evening",
		                  "hey there", "hi there"}
	
	def check(self, chat_ctx):
		texts = [m.text_content.lower().strip() for m in chat_ctx.items if
		         hasattr(m, "role") and m.role == "user" and m.text_content]
		if not texts:
			return False
		last = texts[-1]
		now = time.time()
		if last not in self.greet_set:
			self._recent_greets.clear()
			return False
		self._recent_greets.append((now, last))
		while self._recent_greets and now - self._recent_greets[0][0] > self.greet_window_sec:
			self._recent_greets.popleft()
		if len(self._recent_greets) >= self.greet_max_repeats:
			return True
		return False
