from livekit.agents import llm
from livekit.agents.ipc.inference_executor import InferenceExecutor
from livekit.plugins.silero import VAD
from livekit.plugins.turn_detector.english import EnglishModel

from .turndetector.contextual_answer_detector import ContextualAnswerDetector
from .turndetector.prosodic_backchannel_detector import ProsodicBackchannelDetector
from .turndetector.repeated_greetings_detector import RepeatedGreetingsDetector


class AskTurnDetector(EnglishModel):
	def __init__(
			self,
			inference_executor: InferenceExecutor | None = None,
			unlikely_threshold: float = 0.0289,
			ack_set: set[str] | None = None,
			max_ack_words: int = 2,
			greet_window_sec: float = 2.0,
			greet_max_repeats: int = 2,
			energy_thresh: float = 0.01,
			vad: VAD | None = None,
	) -> None:
		super().__init__(

		)
		self.repeated_greetings = RepeatedGreetingsDetector(greet_window_sec, greet_max_repeats)
		self.contextual_answer = ContextualAnswerDetector(ack_set)
		self.prosodic_backchannel = ProsodicBackchannelDetector(ack_set, max_ack_words, energy_thresh, vad)
	
	async def predict_end_of_turn(
			self,
			chat_ctx: llm.ChatContext,
			*,
			timeout: float | None = 3
	) -> float:
		if self.repeated_greetings.check(chat_ctx):
			return 0.0
		if self.contextual_answer.check(chat_ctx):
			return await super().predict_end_of_turn(chat_ctx, timeout=timeout)
		if await self.prosodic_backchannel.check(chat_ctx):
			return 0.0
		return await super().predict_end_of_turn(chat_ctx, timeout=timeout)
