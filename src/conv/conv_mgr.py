import asyncio
import json
import logging
import uuid
from multiprocessing import Event as Process<PERSON>vent
from threading import Event as ThreadEvent
from typing import Any

import langfuse
import sentry_sdk
from livekit import rtc
from livekit.agents import JobContext, AgentSession, ConversationItemAddedEvent, AgentStateChangedEvent
from livekit.agents import UserStateChangedEvent
from livekit.plugins import openai, silero
from livekit.rtc import RemoteTrackPublication, RemoteParticipant

from agents.agent_conf import get_agent_info_from_meta
from agents.jana_agent import JanaAgent
from agents.settings import AgentSettings
from app.config import get_config
from conv.analysers.analyser import AnalyserConfig
from conv.analysers.analysers import create_analyser
from conv.conv_meta import RoomConfig
from conv.conv_svc import ConvSvc
from conv.features.ask_turn_detector import AskTurnDetector
from conv.features.sentiments_handler import SentimentAnalyzer
from factory.stt_factory import create_stt
from factory.tts_factory import create_tts
from factory.vad_profiles import get_vad_profile
from log import conv_log_svc
from log.conv_log_svc import ConversationLogger
from log.sentry_decorators import sentry_span


def create_event():
    try:
        return ProcessEvent()
    except ImportError:
        return ThreadEvent()


_config = get_config()
_logger = logging.getLogger(__name__)
_logger.propagate = False


@sentry_span(op="room.build_ses_opts", description="build session options")
def build_conv_meta(room: rtc.Room, metadata: str = ''):
    _metadata = metadata if metadata else (room.metadata if len(room.metadata) > 0 else "")
    _logger.debug(f"Using metadata: {_metadata}")

    room_meta = RoomConfig()
    if _metadata:
        try:
            room_meta = RoomConfig().load_json(_metadata)
        except json.JSONDecodeError as e:
            _logger.warning(f"Failed to parse metadata as JSON: {e}")
        except Exception as e:
            _logger.warning(f"Error processing metadata: {e}")

    return room_meta


class ConvManager:
    _tasks = set[asyncio.Task[Any]]()
    _lock = asyncio.Lock()
    meta_updated = create_event()
    conversation_ended = create_event()
    conversation_finalizing = create_event()
    _conversation_logger: ConversationLogger = ConversationLogger()
    _api_client: conv_log_svc = None
    conv_meta: RoomConfig = None
    _raw_metadata: str = None  # Store the raw metadata string

    ctx: JobContext = None
    room: rtc.Room = None

    agent: JanaAgent = None
    user_away_timeout = _config.app.silence_threshold  # Duration in seconds
    is_user_active = False
    langfuse_client = langfuse.Langfuse(public_key=_config.langfuse.public_key,
                                        secret_key=_config.langfuse.secret_key,
                                        host=_config.langfuse.host,
                                        )

    def update_metadata(self, metadata: str):
        self._raw_metadata = metadata  # Store the raw metadata string
        self.conv_meta = build_conv_meta(self.room, metadata)
        self._api_client = ConvSvc(conversation_type=self.conv_meta.type)
        conversation_id = self.conv_meta.context.conversationId
        _logger.debug(f"Successfully created ConvManager with conversation_id: {conversation_id}")
        self.meta_updated.set()

    def get_metadata(self) -> str:
        return self._raw_metadata

    @sentry_span(op="manager", description="wake up")
    def __init__(self, ctx: JobContext, session_id: str = str(uuid.uuid4())):
        self.callee_is_idle = create_event()
        self.sentiment_analyser = None
        self.agent_settings = None
        self.session: AgentSession = None
        self.analyser = None
        self.last_activity = None
        self._is_welcome_played = False
        self.ctx = ctx
        self.room = ctx.room
        self.conversation_finalizing = create_event()
        self.conversation_ended = create_event()
        self.participant_is_joined = create_event()
        self.idle_counter = 0
        self._shutting_down = False

    def send_metrics_to_sentry(self, usageSummary: any):
        metric_name = "voice services summary"
        _logger.info(f"metrics:{metric_name} {usageSummary}")

        span = sentry_sdk.get_current_span()
        if span:
            span.set_data(metric_name, usageSummary)



    @sentry_span(op="agent.setup", description="setup features")
    def start_conversation_workflow(self):
        log_conversation = self._conversation_logger.log_conversation
        config = get_config()

        company_name = self.conv_meta.context.companyName
        analyser_config = AnalyserConfig(prompt=self.conv_meta.analysis.prompt)
        self.sentiment_analyser = SentimentAnalyzer(config=config, core_api_client=self._api_client)
        self.analyser = create_analyser(
            core_api_client=self._api_client,
            company_name=company_name,
            config=analyser_config
        )

        @self.session.on("agent_state_changed")
        def on_agent_state_changed(event: AgentStateChangedEvent):
            if event.old_state == "speaking" and event.new_state == "listening":
                if self.callee_is_idle.is_set():
                    # self.session._user_state = 'listening'
                    self.session._set_user_away_timer()

        @self.session.on("user_state_changed")
        def on_user_state_changed(event: UserStateChangedEvent):
            """
            LiveKit emits this every time the user’s VAD/STT state changes:
              * new_state == "speaking"  → user started speaking
              * new_state == "listening" → user stopped speaking but not yet away
              * new_state == "away"      → user and agent are both silent for user_away_timeout
            """
            if event.new_state == "speaking":
                # User just began talking → reset idle counter and cancel any idle flags
                self.idle_counter = 0
                self.callee_is_idle.clear()
            elif event.new_state == "away":
                # User has been silent for user_away_timeout
                self.idle_counter += 1
                if self.idle_counter <= 3:
                    self.callee_is_idle.set()
                    _logger.debug(
                        f"user_state_changed: Silence #{self.idle_counter}—sending idle message after {self.user_away_timeout}s of silence."
                    )
                    self.agent.say_idle_message()
                else:
                    _logger.debug("user_state_changed: Maximum idle prompts reached—ending conversation.")
                    self.set_end_conversation()

        @self.session.on("conversation_item_added")
        def on_conversation_item_added(event: ConversationItemAddedEvent):
            for content in event.item.content:
                if isinstance(content, str):
                    print(f" - text: {content}")
                    self.add_task(log_conversation(
                        actor=event.item.role,
                        message=content,
                        conversation_type=conversation_type,
                        company_id=company_id,
                        conversation_id=conversation_id,
                    ))
                    if event.item.role == "user":
                        self.add_task(self.sentiment_analyser.process_sentiment_analysis(conversation_id, content))

        conversation_id = self.conv_meta.context.conversationId
        conversation_type = self.conv_meta.type
        company_id = self.conv_meta.context.companyId

    def _create_tts(self, agent_settings: AgentSettings):
        provider = agent_settings.voiceSettings.provider
        return create_tts(provider=provider, agent_settings=agent_settings)

    @sentry_span("agent.init", "init agent")
    async def start_conversation(self):
        agent_config = get_agent_info_from_meta(self.conv_meta)
        self.agent_settings = AgentSettings(agent_info=agent_config,
                                            profile_prompt=self.conv_meta.llm.profilePrompt,
                                            languages=self.conv_meta.llm.language)

        allowed_funcs = self.conv_meta.functions
        # self.session = AgentSession(
        #        llm=openai.realtime.RealtimeModel(voice="echo"),
        # )
        vad_profile = get_vad_profile(self.agent_settings.voiceSettings.vad_profile)
        vad = silero.VAD.load(**vad_profile.to_dict())
        self.session = AgentSession(
            stt=create_stt(language=self.conv_meta.llm.language),
            llm=openai.LLM(model=self.conv_meta.llm.model, temperature=_config.openai.temperature),
            tts=self._create_tts(self.agent_settings),
            vad=vad,
            min_interruption_words=2,
            min_interruption_duration=1,
            turn_detection=AskTurnDetector(vad=vad),
            user_away_timeout=self.user_away_timeout
        )
        self.start_conversation_workflow()
        self.agent = agent = JanaAgent(
            opts=self.agent_settings,
            conv_id=self.conv_meta.context.conversationId,
            company_id=self.conv_meta.context.companyId,
            timezone=self.conv_meta.context.timezone,
            analyser=self.analyser,
            api_client=self._api_client,
            room=self.room,
        )
        await self.agent.initialize()
        _logger.debug(f"start_conversation: agent initialized")
        await agent.update_agent_attributes({"agent.status": "ready"})
        _logger.info(
            f"agent {agent.agent_settings.config.name}:  {agent.agent_settings.config.id} started listening in room")
        return self.agent

    def set_end_conversation(self):
        self.conversation_ended.set()
        self.conversation_finalizing.set()
        self.add_task(self.agent.finish_call(self.session))

    def add_task(self, task):
        asyncio.create_task(self._add_task_async(task))

    async def _add_task_async(self, task):
        if not asyncio.iscoroutine(task):
            raise ValueError("Task must be an awaitable coroutine")
        async_task = asyncio.create_task(task)
        async with self._lock:
            self._tasks.add(async_task)
        return async_task

    @sentry_span(op="room", description="wait callee")
    async def wait_for_participant(self):
        """
        Wait for a participant to join the room and be fully available.
        This method handles waiting for participant tracks and monitoring call status changes.
        """
        # Create a future that will be resolved when participant is fully available
        future = asyncio.get_event_loop().create_future()

        participant_obj = await self.ctx.wait_for_participant()
        # Check if participant already has tracks
        if len(participant_obj.track_publications) > 0:
            self.participant_is_joined.set()
            if not future.done():
                future.set_result(participant_obj)
        else:
            # Event handler for track publications
            @self.ctx.room.on("track_published")
            @sentry_span(op="room.track_published", description="callee added track")
            def track_published(publication: RemoteTrackPublication, participant: RemoteParticipant):
                _logger.info(f"{participant.identity} published track: {publication.name}")
                self.participant_is_joined.set()
                if not future.done():
                    future.set_result(participant_obj)

        @sentry_span(op="participant.attributes_changed", description="callee changed state")
        @self.ctx.room.on("participant_attributes_changed")
        def participant_attributes_changed(changed_attributes, participant):
            _logger.info(f"{participant.identity} attributes changed {changed_attributes}")
            call_status_changed_to = changed_attributes.get('sip.callStatus', '')
            if call_status_changed_to == 'active':
                self.participant_is_joined.set()
                _logger.info("callStatus: active")
                # Check if we can resolve the future
                if participant_obj and len(participant_obj.track_publications) > 0 and not future.done():
                    future.set_result(participant_obj)
            elif call_status_changed_to == 'hangup':
                self.set_end_conversation()
                _logger.info("callStatus: hangup. ending conversation")
                if not future.done():
                    future.set_exception(Exception("Call hungup before participant was fully available"))
            else:
                _logger.info(f"callStatus: {call_status_changed_to} (no action taken)")

        try:
            # Wait for the future to be resolved with a timeout
            await asyncio.wait_for(future, timeout=160.0)  # 160 second timeout
            participant = future.result()
            _logger.info(
                f"Participant fully available: {participant} conv:{self.conv_meta.context.conversationId}")
            return participant
        except asyncio.TimeoutError:
            _logger.warning("Timed out waiting for participant to be fully available")
            # Return the participant anyway, even if not fully ready
            if participant_obj:
                _logger.info(f"Returning participant despite timeout: {participant_obj}")
                return participant_obj
            raise Exception("No participant available after timeout")