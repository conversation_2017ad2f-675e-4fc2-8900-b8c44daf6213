import logging
from base64 import b64encode
from typing import Dict, Any, Optional

import aiohttp
import sentry_sdk

from app.base_svcs import get_now_s
from app.config import get_config
from app.sentry_config import capture_errors
from log.sentry_decorators import sentry_span  # Import the decorator

_logger = logging.getLogger(__name__)
_logger.propagate = False

class ConversationLogger:
    def __init__(self):
        api_conf = get_config().conversation_api
        self.base_url = api_conf.url

        credentials = f"{api_conf.login}:{api_conf.password}"
        self.auth = b64encode(credentials.encode()).decode()

        self.headers = {
            "Authorization": f"Basic {self.auth}",
            "Content-Type": "application/json",
        }

    async def load_conv_history(self, conversation_id, limit=20, cursor=None):
        try:
            import json
            import os

            file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'conv-1.json')
            with open(file_path, 'r') as f:
                all_results = json.load(f)

            # Filter by conversation_id if provided
            if conversation_id:
                all_results = [msg for msg in all_results if msg.get('conversationId') == conversation_id]

            # Apply limit if specified
            if limit and limit > 0:
                all_results = all_results[:limit]

            return all_results

        except Exception as e:
            _logger.error(f"Error loading conversation history from file: {e}")
            return []

    async def load_conv_history_1(self, conversation_id, limit=20, cursor=None):
        all_results = []
        current_cursor = cursor
        has_more = True

        while has_more:
            async with aiohttp.ClientSession(headers=self.headers) as session:
                url = f"{self.base_url}/v1/conversation-logs"
                params = {
                    "conversationId": conversation_id,
                    "limit": limit
                }
                if current_cursor:
                    params["cursor"] = current_cursor

                _logger.debug(f"Sending GET request to URL: {url} with params: {params}")

                async with session.get(url, params=params) as response:
                    response.raise_for_status()
                    full_response = await response.json()

                    # Extract data and add to results
                    page_data = full_response.get("data", [])
                    all_results.extend(page_data)

                    # Check if there are more pages
                    meta = full_response.get("meta", {})
                    has_more = meta.get("hasMore", False)
                    current_cursor = meta.get("cursor")

                    # If no more data or no cursor for next page, exit loop
                    if not has_more or not current_cursor:
                        break

        return all_results
      

    @capture_errors
    @sentry_span(op="logging", description="log conversation")
    async def log_conversation(
            self,
            company_id: str,
            conversation_id: str,
            conversation_type: str,
            message: str,
            actor: str,
    ) -> Optional[Dict[str, Any]]:
        if not message:
            _logger.debug("Empty message received. Skipping logging.")
            return None

        # Normalize actor names
        actor = "agent" if actor == "assistant" else "callee"

        # Prepare the data payload
        data = {
            "companyId": company_id,
            "conversationId": conversation_id,
            "message": message,
            "actor": actor,
            "timestamp": get_now_s(),
        }

        # Update headers with conversation type
        headers = self.headers.copy()  # Use a copy to prevent side-effects
        headers["conversation-type"] = conversation_type
        current_span = sentry_sdk.Hub.current.scope.span
        try:

            async with aiohttp.ClientSession(headers=headers) as session:
                url = f"{self.base_url}/v1/conversation-logs"
                _logger.debug(f"Sending POST request to URL: {url} with Data: {data}")

                async with session.post(url, json=data) as response:
                    # Access the current span from Sentry

                    if current_span:
                        current_span.set_data("http.status_code", response.status)

                    response.raise_for_status()
                    result = await response.json()

                    # Optionally sanitize the response before logging
                    sanitized_result = self._sanitize_response(result)
                    if current_span:
                        current_span.set_data("response_body", sanitized_result)

                    _logger.info(f"Successfully logged conversation: {data} Response: {sanitized_result}")
                    return sanitized_result
        except aiohttp.ClientError as http_err:
            _logger.error(f"HTTP error occurred while logging conversation: {http_err}")
            sentry_sdk.capture_exception(http_err)
            if current_span:
                current_span.set_status("error")
            return None
        except Exception as e:
            _logger.exception(f"Unexpected error occurred while logging conversation: {e}")
            sentry_sdk.capture_exception(e)
            if current_span:
                current_span.set_status("error")
            return None

    def _sanitize_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        sensitive_fields = ["password", "auth_token"]
        sanitized = response.copy()
        for field in sensitive_fields:
            if field in sanitized:
                sanitized[field] = "MASKED"
        return sanitized