import asyncio
import functools
from typing import Callable, Optional, Any, TypeVar

import sentry_sdk

F = TypeVar('F', bound=Callable[..., Any])

def sentry_transaction(op: Optional[str] = None, name: Optional[str] = None) -> Callable[[F], F]:
    def decorator(func: F) -> F:
        is_coroutine = asyncio.iscoroutinefunction(func)

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            transaction_op = op if op else f"{func.__module__}.{func.__name__}"
            transaction_name = name if name else func.__name__
            with sentry_sdk.start_transaction(op=transaction_op, name=transaction_name) as transaction:
                try:
                    result = await func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                except Exception as e:
                    transaction.set_status("error")
                    sentry_sdk.capture_exception(e)
                    raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            transaction_op = op if op else f"{func.__module__}.{func.__name__}"
            transaction_name = name if name else func.__name__
            with sentry_sdk.start_transaction(op=transaction_op, name=transaction_name) as transaction:
                try:
                    result = func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                except Exception as e:
                    transaction.set_status("error")
                    sentry_sdk.capture_exception(e)
                    raise

        return async_wrapper if is_coroutine else sync_wrapper  # type: ignore

    return decorator

def sentry_span(op: Optional[str] = None, description: Optional[str] = None) -> Callable[[F], F]:
    def decorator(func: F) -> F:
        is_coroutine = asyncio.iscoroutinefunction(func)

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            span_op = op if op else f"{func.__module__}.{func.__name__}"
            span_description = description if description else func.__name__
            with sentry_sdk.start_span(op=span_op, name=span_description) as span:
                try:
                    result = await func(*args, **kwargs)
                    span.set_status("ok")
                    span.finish()
                    return result
                except Exception as e:
                    span.set_status("error")
                    span.finish()
                    sentry_sdk.capture_exception(e)
                    raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            span_op = op if op else f"{func.__module__}.{func.__name__}"
            span_description = description if description else func.__name__
            with sentry_sdk.start_span(op=span_op, name=span_description) as span:
                try:
                    result = func(*args, **kwargs)
                    span.set_status("ok")
                    span.finish()
                    return result
                except Exception as e:
                    span.set_status("error")
                    sentry_sdk.capture_exception(e)
                    span.finish()
                    raise

        return async_wrapper if is_coroutine else sync_wrapper  # type: ignore

    return decorator