import logging
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, Optional

from app.base_svcs import get_langfuse, get_now

logger = logging.getLogger(__name__)
logger.propagate = False

@dataclass
class VoiceMonitoringMetrics:
    start_time: float = 0
    audio_duration: float = 0
    latency: float = 0
    model_info: Dict[str, Any] = None

    def start(self) -> None:
        self.start_time = time.time()

    def stop(self) -> float:
        """Stop timing and return latency"""
        self.latency = time.time() - self.start_time
        return self.latency


class VoiceMonitoring:

    def __init__(
            self,
            company_id: str,
            conversation_id: str,
            agent_config: Dict[str, Any]
    ):
        self.langfuse = get_langfuse()
        self.company_id = company_id
        self.conversation_id = conversation_id
        self.agent_config = agent_config

        # Active traces
        self.conversation_trace = None
        self.current_generation = None
        self.metrics = VoiceMonitoringMetrics()

        logger.info("Initialized voice monitoring")

    def start_conversation(
            self,
            room_name: str,
            participant_id: str,
            metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Start a new conversation trace"""
        try:
            # Create main conversation trace
            self.conversation_trace = self.langfuse.trace(
                name="voice_conversation",
                user_id=self.company_id,
                session_id=self.conversation_id,
                metadata={
                    "room_name": room_name,
                    "participant_id": participant_id,
                    "agent_name": self.agent_config["name"],
                    "agent_id": self.agent_config["id"],
                    "model": self.agent_config["model"],
                    "start_time": get_now(),
                    **(metadata or {})
                }
            )
            logger.info(f"Started conversation trace: {self.conversation_trace.id}")

        except Exception as e:
            logger.error(f"Error starting conversation trace: {e}")
            raise

    async def track_speech_to_text(
            self,
            audio_duration: float,
            transcription: str,
            model_info: Dict[str, Any]
    ) -> None:
        """Track a speech-to-text operation"""
        if not self.conversation_trace:
            return

        try:
            # Create STT span
            span = self.conversation_trace.span(
                name="speech_to_text",
                start_time=datetime.fromtimestamp(self.metrics.start_time),
                metadata={
                    "model": model_info.get("name", "unknown"),
                    "audio_duration": audio_duration,
                    "latency": self.metrics.latency
                },
                input={
                    "audio_duration": audio_duration
                },
                output={
                    "transcription": transcription
                }
            )
            logger.debug(f"Tracked STT operation: {span.id}")

        except Exception as e:
            logger.error(f"Error tracking STT: {e}")
            await self.track_error("stt_error", e)

    async def track_llm_start(
            self,
            messages: list,
            model: str
    ) -> None:
        """Track start of LLM generation"""
        if not self.conversation_trace:
            return

        try:
            self.current_generation = self.conversation_trace.generation(
                name="llm_response",
                start_time=datetime.now(),
                model=model,
                input={"messages": messages},
                metadata={
                    "type": "llm_generation",
                    "streaming": True
                }
            )
            self.metrics.start()

        except Exception as e:
            logger.error(f"Error tracking LLM start: {e}")
            await self.track_error("llm_start_error", e)

    async def track_llm_end(
            self,
            completion: str,
            metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Track completion of LLM generation"""
        if not self.current_generation:
            return

        try:
            await self.current_generation.update(
                end_time=datetime.now(),
                output={"completion": completion},
                metadata={
                    "latency": self.metrics.stop(),
                    **(metadata or {})
                }
            )
            self.current_generation = None

        except Exception as e:
            logger.error(f"Error tracking LLM end: {e}")
            await self.track_error("llm_end_error", e)

    async def track_text_to_speech(
            self,
            text: str,
            audio_duration: float,
            voice_id: str,
            metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Track a text-to-speech generation"""
        if not self.conversation_trace:
            return

        try:
            # Create TTS span
            span = self.conversation_trace.span(
                name="text_to_speech",
                start_time=datetime.fromtimestamp(self.metrics.start_time),
                metadata={
                    "voice_id": voice_id,
                    "audio_duration": audio_duration,
                    "latency": self.metrics.latency,
                    **(metadata or {})
                },
                input={"text": text},
                output={"audio_duration": audio_duration}
            )
            logger.debug(f"Tracked TTS operation: {span.id}")

        except Exception as e:
            logger.error(f"Error tracking TTS: {e}")
            await self.track_error("tts_error", e)

    async def track_error(
            self,
            error_type: str,
            error: Exception,
            metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Track an error event"""
        if not self.conversation_trace:
            return

        try:
            await self.conversation_trace.event(
                name=error_type,
                level="ERROR",
                status_message=str(error),
                metadata={
                    "error_type": error.__class__.__name__,
                    "timestamp": get_now(),
                    **(metadata or {})
                }
            )
            logger.error(f"Tracked error: {error_type} - {error}")

        except Exception as e:
            logger.error(f"Error tracking error event: {e}")

    async def end_conversation(
            self,
            metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """End the conversation trace"""
        if not self.conversation_trace:
            return

        try:
            await self.conversation_trace.update(
                end_time=datetime.now(),
                metadata={
                    "status": "completed",
                    "end_time": get_now(),
                    **(metadata or {})
                }
            )
            logger.info(f"Ended conversation trace: {self.conversation_trace.id}")

        except Exception as e:
            logger.error(f"Error ending conversation: {e}")
            await self.track_error("end_conversation_error", e)