import logging
from typing import Any, AsyncIterable

import sentry_sdk
from livekit import rtc
from livekit.agents import Agent, ModelSettings, function_tool, RunContext, AgentSession
from livekit.agents.llm import llm
from livekit.api import LiveKitAPI
from livekit.protocol.room import UpdateParticipantRequest

from agents.settings import AgentSettings
from app.base_svcs import get_langfuse
from app.config import get_config
from conv.analysers.analyser import CallAnalyser
from log.sentry_decorators import sentry_span

_config = get_config()
_logger = logging.getLogger(__name__)
_logger.propagate = False
_langfuse = get_langfuse()

class JanaAgent(Agent):
    def __init__(
            self,
            opts: AgentSettings,
            conv_id: str,
            company_id: str,
            timezone: str,
            analyser: CallAnalyser,
            api_client: Any,
            room: rtc.Room,
    ) -> None:
        self.room = room
        self.agent_settings = opts
        self.langfuse = get_langfuse()
        self.conv_id = conv_id
        self.company_id = company_id
        self.timezone = timezone
        self.analyser = analyser
        self.api_client = api_client
        self.trace = None
        self._ending = None

        super().__init__(
            instructions=opts.get_system_prompt(),
            # turn_detection="stt"
        )
        self._initial_tools = [
            self.schedule_callback,
            self.donot_call,
            self.end_conversation
        ]

        # Add send_follow_up_message if specified in metadata
        if "send_follow_up_message" in opts.config.functions:
            self._initial_tools.append(self.send_follow_up_message)
        _logger.debug(f"Prepared tools: {[t.__name__ for t in self._initial_tools]}")

    async def initialize(self):
        _logger.info(f"Initializing agent with tools: {[t.__name__ for t in self._initial_tools]}")
        await self.update_tools(self._initial_tools)

    async def llm_node(
            self,
            chat_ctx: llm.ChatContext,
            tools: list[llm.FunctionTool],
            model_settings: ModelSettings,
    ) -> AsyncIterable[llm.ChatChunk]:
        # user_message = next(
        #     (message.content for message in reversed(chat_ctx.messages) if message.role == "user"),
        #     ""
        # )
        # rag_context = await RAGSearch.enrich(chat_ctx, user_message)
        # chat_ctx.add_message(content=rag_context, role="system")
        # update the context for persistence
        # await self.update_chat_ctx(chat_ctx)

        return Agent.default.llm_node(self, chat_ctx, tools, model_settings)

    @function_tool
    @sentry_span(op="func_call", description="followup conversation")
    async def send_follow_up_message(self, context: RunContext):
        """When the user requests a follow-up or details, so agent will send a message to current phone number using WhatsApp. Agent will send a followup to whatsapp and quickly confirm that"""
        await self.api_client.request_follow_up(conversation_id=self.conv_id)
        _logger.info(f"exec: send_follow_up_message")
        # await self.finalize_conversation_fn()
        return "follow up requested. agent will disconnect after saying goodbye"

    @function_tool
    @sentry_span(op="func_call", description="schedule_callback function call")
    async def schedule_callback(self, context: RunContext, date_time: str) -> str:
        """When the user requests a callback at a some date and time, schedule the callback in the system and responds with just a few words of confirmation.
          Args:
              "date_time": Specifies the date and time to recall to this client in ISO format. Use your knowledge about current date and time. If a specific time is not mentioned, assume 10:00 AM.If no date is specified, assume the next business day.Ensure the datetime is in the future and within business hours"
        """
        _logger.warning(f"exec: schedule_callback {date_time}")
        await self.api_client.request_callback(conversation_id=self.conv_id, callback_time=date_time)
        return "callback where scheduled. agent should say goodbye"

    @function_tool
    @sentry_span(op="func_call", description="donotcall function call")
    async def donot_call(self, context: RunContext):
        """Set the conversation status to ‘Do Not Call’ when the user explicitly states that they do not wish to receive further communication, such as by saying ‘do not call again,’ ‘please remove me from your call list,’ or any equivalent phrase indicating they do not want to be contacted again"""
        _logger.info(f"exec: donot_call")
        await self.api_client.set_do_not_call(conversation_id=self.conv_id)
        await self.finish_call(context.session)
        return "callee do not want to talk again. agent should say goodbye and quickly finish"

    async def finish_call(self, session: AgentSession):
        await self.analyser.build_conversation_result(
            conversation_id=self.conv_id,
            conversation_history=session.history
        )
        await self.update_agent_attributes({"agent.callStatus": "endCall"})
        await session.aclose()
        await self.room.disconnect()

    @function_tool
    @sentry_span(op="func_call", description="end conversation function call")
    async def end_conversation(self, context: RunContext):
        """End conversation, when user do not want to continue or want to terminate the session."""
        await context.session.generate_reply(
            instructions="say goodbye to the user using common friendy but formal phrases")
        await self.finish_call(context.session)

    async def update_agent_attributes(self, attributes: dict) -> None:
        try:
            lkapi = LiveKitAPI()
            await lkapi.room.update_participant(
                UpdateParticipantRequest(
                    room=self.room.name,
                    identity=self.room.local_participant.identity,
                    attributes=attributes,
                ),
            )
            await lkapi.aclose()
        except Exception as e:
            _logger.error(f"Failed to update participant attributes: {e}")
            sentry_sdk.capture_exception(e)


    async def tts_node(
            self, text: AsyncIterable[str], model_settings: ModelSettings
    ) -> AsyncIterable[llm.ChatChunk]:
        return Agent.default.tts_node(self, text, model_settings)

    @sentry_span(op="agent.say_idle_message", description="Agent Say Idle Message")
    def say_idle_message(self):
        try:
            if hasattr(self, 'session') and self.session is not None and self.session._activity is not None:
                self.session.generate_reply(instructions="confirm if user is still there")
        except Exception as e:
            logging.getLogger(__name__).warning(f"Failed to say idle message: {e}")
