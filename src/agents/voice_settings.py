import json
from typing import Union, Dict, Any

from livekit.plugins import elevenlabs

from agents.language_settings import LanguageSettings


class VoiceSettings:
    def __init__(self, voice_settings: Union[str, Dict[str, Any], None] = None):
        if isinstance(voice_settings, str):
            if voice_settings.strip() == '':
                voice_settings = {}
            else:
                try:
                    voice_settings = json.loads(voice_settings)
                except json.JSONDecodeError:
                    raise ValueError("voiceSettings is not valid JSON.")
        elif voice_settings is None:
            voice_settings = {}
        elif not isinstance(voice_settings, dict):
            raise TypeError("voiceSettings must be a dictionary, JSON string, or None.")

        self.provider = voice_settings.get("provider", "11labs")
        self.stability = voice_settings.get("stability", 0.8)
        self.similarity_boost = voice_settings.get("similarity_boost", 0.8)
        self.style = voice_settings.get("style", 0.6)
        self.use_speaker_boost = voice_settings.get("use_speaker_boost", True)

        lang_settings = LanguageSettings(voice_settings.get("language", "eng"))
        lang_name, _ = lang_settings.map_language_code(lang_settings.languages[0])
        self.language = lang_name

    def to_elevenlabs_voice_settings(self) -> elevenlabs.tts.VoiceSettings:
        return elevenlabs.tts.VoiceSettings(
            stability=self.stability,
            similarity_boost=self.similarity_boost,
            style=self.style,
            use_speaker_boost=self.use_speaker_boost,
        )
