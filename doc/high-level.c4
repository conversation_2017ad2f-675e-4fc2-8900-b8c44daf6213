// Tutorial reference:
// https://likec4.dev/tutorial/

specification {
  element system
  element actor
  element container
  element component
  {
    style {
        color sky
    }
  }
}

model {
  customer = actor 'Subscriber' {
     style { shape person
     color indigo
      }
    description 'The regular subscriber of the system'
  }

  actor CallResultsAnalyser 'Call Results Analyser' {
    component AnalyseConversation 'Result Analyser'
  }

  actor Administrator 'Administrator' {
    style { 
        shape person 
        color indigo
    }
  }

  system TelephonyProviders 'Telephony Providers' {
    style { 
      shape rectangle
    }
  }

  system Callevate ''{
    style {
        color sky
    }
     system Agent_system 'Agent-server'{
     style {
            color sky
        }
    container Agent_instance 'Agent host'{
        description 'host 3 agents by default'
       
    }
    component AgentFunctions 'Agent Functions' {
      component send_followup 'Sends followup'
      component schedule_call_back 'Schedule Callback'
      component do_not_call 'Do not call'
      component SentimentsModule 'Sentiments Analyser'
       -> Callevate.API
    }
    component VAD 'Voice Auto Detection' {
      technology 'Silero VAD'
    }
    component EOU 'End of utterance detection'
    component STT_plug 'ASR and Transcription Plugin' {
      component stt_deepgram 'Deepgram Streaming'
      component stt_google 'OpenAI Whisper Files'
      component stt_nemo 'NVidia Nemo ASR Streaming' {
        description 'Experimental'
      }
    }
    component TTS_plug 'Voice synthesis plugin' {
      component tts_11labs 'ElevenLabs'
      component tts_cartesia 'Cartesia'
    }
    component LLM_plug 'LLM plugin' {
      component OpenAI_comp 'OpenAI API compatible'
      component ollama_comp 'Ollama compatible'
    }
    component Agent_Workflow 'Agent Workflow' {
      component Workflow_engine 'Configure dialog rules'
    }

  }
     container ManagementSystem 'Management System' {
      style { 
        shape rectangle
        color sky
      }
      component API 'API' {
        style {
            color sky
        }
        technology 'NodeJS'
        -> Database ''
        -> ExternalAPI
        -> Agent_Workflow
      }
      component Frontend 'Front' {
        style {
            color sky
        }
        technology 'react, websockets'
        -> API ''
      }
        container Database 'Database' {
      description 'Stores room & participant data & conversations'
      technology 'PostgreSQL'
    }
    container ConversationLogDatabase 'Conversation Log Database' {
      description 'Stores Conversation logs'
      technology 'MongoDB'
    }
        -> CallResultsAnalyser
    }
    container ExternalAPI 'External API' {
      technology 'NodeJS'
      -> Database ''
      -> Campaings 'schedule calls'
    }
    container Campaings 'Campaigns Run Service' {
      style { 
        shape rectangle
      }
    }
  }
  system AI_Infrastructure 'AI Infrastructure Servers'{
    style {
        color sky
    }
    container deepgram_services 'STT services'
    container Elabs_services 'TTS Services'
    container ollama_server 'OpenAI API compatible server'
    container nemo_server 'Nemo ASR'
    container openai_server 'OpenAI'

  }
 
 
  system RT_room_server 'RT-room-server' {
    style { 
            color sky
        } 
    system  TelephonyGateway 'Telephony Gateway' {
      description 'Bridges PSTN/SIP'
      style {
        
      }
      component SIPTrunk 'SIP Trunk' {
        description 'SIP'
      }
      component IVRService 'IVR Service' {
        description 'Manages interactive voice response flows'
      }
    }
    container RoomService 'Room Service' {
      description 'Manages room lifecycle & config'
      technology 'gRPC'
    }
  }

  // Relationships
  Agent_system -> RT_room_server.RoomService 'media streams with room services'
  RT_room_server.TelephonyGateway -> TelephonyProviders 'SIP integration'
  RT_room_server.RoomService -> Callevate.Database 'Reads/writes config and conversations state'
  Administrator -> Frontend 'Manages configuration and campaigns'
  customer -> TelephonyProviders 'Incoming calls'
  TelephonyProviders -> customer 'Outcoming calls'
  // Added relationship for incoming calls from TelephonyProviders
  
  //Agent_system.LLM_plug.OpenAI_comp -> AI_Infrastructure.openai_server
  Agent_system.LLM_plug.ollama_comp -> AI_Infrastructure.ollama_server
  Agent_system.STT_plug.stt_deepgram -> AI_Infrastructure.deepgram_services
  Agent_system.TTS_plug.tts_11labs -> AI_Infrastructure.Elabs_services
  //Agent_system.STT_plug.stt_nemo -> AI_Infrastructure.nemo_server

}

views {
// Container View: Shows containers within RT_room_server and external interactions
  /**
   * @likec4-generated(v1)
   * iKRoYXNo2ShjYjhmNzA0OTdkYjI4NjUyYTk3MGJkOTIyMDc0ZjQ5YTVmN2U3ZDI5qmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJUQqF4/KF50Lqld2lkdGjNB2GmaGVpZ2h0zQVdpW5vZGVzj6hjdXN0b21lcoKhYpT80LrNAUDMtKFjwq1BZG1pbmlzdHJhdG9ygqFilM0C/tC9zQFAzLShY8KpQ2FsbGV2YXRlgqFilM0CHcz3zQNNzQJkoWPDrlJUX3Jvb21fc2VydmVygqFilADNAd7NAbbNAjGhY8OxQUlfSW5mcmFzdHJ1Y3R1cmWCoWKUzQWjzJDNAbrNA0mhY8OyVGVsZXBob255UHJvdmlkZXJzgqFilBHMu80BQMy0oWPCtkNhbGxldmF0ZS5BZ2VudF9zeXN0ZW2CoWKUzQM3zQJ/zQFAzLShY8K6Q2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW2CoWKUzQJFzQEuzQL9zQEl
   * oWPDulJUX3Jvb21fc2VydmVyLlJvb21TZXJ2aWNlgqFilE7NAzPNAUDMtKFjwr9SVF9yb29tX3NlcnZlci5UZWxlcGhvbnlHYXRld2F5gqFilCjNAhvNAUDMtKFjwr9BSV9JbmZyYXN0cnVjdHVyZS5vbGxhbWFfc2VydmVygqFilM0Fy80C/c0BQMy0oWPC2SBBSV9JbmZyYXN0cnVjdHVyZS5FbGFic19zZXJ2aWNlc4KhYpTNBeTNAeXNAUDMtKFjwtkjQUlfSW5mcmFzdHJ1Y3R1cmUuZGVlcGdyYW1fc2VydmljZXOCoWKUzQX1zM3NAUDMtKFjwtkjQ2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW0uRnJvbnRlbmSCoWKUzQJtzQF3zQFAzLShY8K+Q2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW0uQVBJgqFilM0D2s0BZc0BQMy0oWPCpWVkZ2Vzi6Zsc2Nrb22DomNwkYKh
   * eMtAhbwAAAAAAKF5y0CI87RnFlXooWyEoXjNAmOhec0DM6V3aWR0aMzYpmhlaWdodBKhcJeSzQLfzQTlks0Cqc0EyJLNAm3NBKaSzQI4zQSEks0B8c0EVZLNAaTNBB2SzQFnzQPtpzFyZDkyc3mDomNwkYKheMy+oXnNAbKhbISheMy/oXnNAcWld2lkdGhgpmhlaWdodBKhcJSSzN3NAhySzOfNAe2SzPTNAbSSzP/NAYSmYnZzcHZvg6JjcJGCoXhroXnMmqFshKF4a6F5zJWld2lkdGhdpmhlaWdodBKhcJSSzQJOzJ2SzQIhzLCSzQHuzMWSzQHAzNmnMXFsbTU2c4OiY3CRgqF4zOChecyboWyEoXjM3qF5zJOld2lkdGhopmhlaWdodBKhcJSSzQG2zN2SzQHkzMqSzQIWzLWSzQJFzKGmeDQxcHRzgqJjcJGCoXjNBN2hectAiFglPIJTyKFwl5LNBB/NBQGSzQRvzQTiks0E
   * 0c0Et5LNBSHNBISSzQV6zQRKks0F080D+JLNBhHNA7imOHh2eHNngqJjcJGCoXjLQJOSAAAAAAChectAhONLHkWDLaFwmpLNBB/NBROSzQR1zQT4ks0E3M0Ey5LNBSHNBISSzQWnzQP6ks0FVM0DkZLNBcHNAvOSzQXWzQLVks0F8M0CuZLNBgrNAqCnMTFyYnQ3b4KiY3CRgqF4y0CTj1JYPkoWoXnLQIGWZmZmZmahcJqSzQQfzQUXks0Ed80E/ZLNBN/NBNCSzQUhzQSEks0F780Dl5LNBTrNAumSzQXazQHbks0F7M0BvZLNBgTNAaGSzQYdzQGIpzFidG50cWiComNwkYKheMtAkHLVg9ARbqF5y0CDAzMzMzMzoXCUks0DoM0E3pLNA8bNBHiSzQQDzQPSks0EKs0DaKZtY2R0NDmComNwkYKheMtAkNEqfC/ukqF5y0CBvMzMzMzNoXCUks0ELs0DXpLNBAjNA8SSzQPLzQRq
   * ks0DpM0E1KcxOTFuYXhvgqJjcJGCoXjLQI3UAAAAAAChectAfIcZ82AWcqFwlJLNA4LNA56SzQObzQOLks0Dts0Dd5LNA8/NA2SmdWN0Njk0g6JjcJGCoXjLQIs1hTZxhTahectAZ/MzMzMzM6FshKF4zQNWoXnM9KV3aWR0aMz0pmhlaWdodBKhcJSSzQM2zQHMks0DLc0CQ5LNAxzNAxeSzQMSzQOT
   */
  view container_view {
    title 'Container View'
    include RT_room_server.*, customer, Agent_system, AI_Infrastructure, TelephonyProviders
    include AI_Infrastructure.ollama_server, AI_Infrastructure.Elabs_services, AI_Infrastructure.deepgram_services
    include Callevate, Callevate.API, Callevate.Frontend, Administrator
    style RT_room_server.** {
      display none
    }
    style TelephonyProviders {
      shape rectangle
      color slate
    }
  }
  /**
 * @likec4-generated(v1)
 * iKRoYXNo2Sg1ZWEzOTgxODQ0ZTZjZDhmNDk5MWU2ZTBiOTIzZmQ2NGQ4NTI3MDAwqmF1dG9MYXlvdXSDqWRpcmVjdGlvbqJCVKdub2RlU2VweKdyYW5rU2VwzJaheMyWoXnR/rKld2lkdGjNC5emaGVpZ2h0zQk/pW5vZGVzjLpSVF9yb29tX3NlcnZlci5Sb29tU2VydmljZYKhYpTNAzrR/rLNAUDMtKFjwqlDYWxsZXZhdGWCoWKUzKz3zQjOzQUyoWPDs0NhbGxSZXN1bHRzQW5hbHlzZXKCoWKUzQU50f6yzQFAzLShY8K2Q2FsbGV2YXRlLkFnZW50X3N5c3RlbYKhYpTM1C7NCH7NBNOhY8O6Q2FsbGV2YXRlLkFnZW50X3N5c3RlbS5WQUSCoWKUzQRMzI/NAUDMtKFjwrpDYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkVPVYKhYpTM/M0Bvc0BQMy0oWPCv0NhbGxldmF0ZS5B
 * Z2VudF9zeXN0ZW0uU1RUX3BsdWeCoWKUzQI6zIDNAUDMtKFjwr9DYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkxMTV9wbHVngqFilM0DQs0EJc0BQMy0oWPCv0NhbGxldmF0ZS5BZ2VudF9zeXN0ZW0uVFRTX3BsdWeCoWKUzQfqzQHdzQFAzLShY8LZJUNhbGxldmF0ZS5BZ2VudF9zeXN0ZW0uQWdlbnRGdW5jdGlvbnOCoWKUzQOMzQHpzQGQzQEToWPD2SVDYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkFnZW50X1dvcmtmbG93gqFilM0H5GXNAUDMtKFjwtk2Q2FsbGV2YXRlLkFnZW50X3N5c3RlbS5BZ2VudEZ1bmN0aW9ucy5TZW50aW1lbnRzTW9kdWxlgqFilM0DtM0CIM0BQMy0oWPCpWVkZ2VzjqdzdGVwLTAxg6JjcJGCoXjLQIOuZmZmZmahecvAbZAAAAAAAKFshKF4zQgS
 * oXnNBpGld2lkdGjM2aZoZWlnaHQToXCUks0JRc0HupLNCSTNB5eSzQj8zQdvks0I1c0HR6dzdGVwLTAyg6JjcJGCoXjLQIpszMzMzM2hectAbTMzMzMzM6FshKF4zQGxoXnNA1ild2lkdGjNAQmmaGVpZ2h0IqFwlJLNB5DNBnWSzQZzzQYuks0EIs0FmpLNAv7NBVGnc3RlcC0wM4OiY3CRgqF4y0B1tmZmZmZmoXnLQHXeZmZmZmahbISheM0BlaF5zQE8pXdpZHRozPumaGVpZ2h0I6FwlJLNAlTNBMySzQJUzQSVks0CVM0ETJLNAlTNBBOnc3RlcC0wNIOiY3CRgqF4y0CFWzMzMzMzoXnLQG+ZmZmZmZqhbISheM0CK6F5zQLLpXdpZHRozPqmaGVpZ2h0I6FwmpLNAi7NA1WSzQIizQMsks0CH80C/ZLNAjvNAtySzQKDzQKGks0Czc0C3ZLNAzTNArGSzQNFzQKqks0DVc0C
 * oJLNA2XNApanc3RlcC0wNYOiY3CRgqF4y0CMjmZmZmZmoXnLQIXYzMzMzM2hbISheM0E3qF5zQLOpXdpZHRozQEXpmhlaWdodCOhcJqSzQRHzQKRks0EWc0CnJLNBGrNAqeSzQR8zQKxks0Ers0Cy5LNBMjNAriSzQTzzQLcks0FF80C+ZLNBTPNAySSzQVHzQNLp3N0ZXAtMDaDomNwkYKheMtAkp5mZmZmZqF5y0CLrmZmZmZmoWyEoXjNBy+hec0DOKV3aWR0aMz9pmhlaWdodCOhcJeSzQYPzQOQks0GgM0Dd5LNBxnNA0ySzQeTzQMJks0Hys0C65LNCADNAsCSzQgszQKYp3N0ZXAtMDeDomNwkYKheMtAisGZmZmZmqF5y0CLyzMzMzMzoWyEoXjNBBKhec0DWqV3aWR0aM0BDaZoZWlnaHQToXCXks0Ez80DnpLNBG7NA4uSzQP5zQNjks0Du80DCZLNA6vNAvKSzQOnzQLX
 * ks0Dqs0Cu6dzdGVwLTA4g6JjcJGCoXjLQIuuZmZmZmahectAhKTMzMzMzaFshKF4zQSAoXnNA5ald2lkdGjM+aZoZWlnaHQjoXCXks0EfM0CsZLNBOHNAteSzQYVzQKMks0GXs0C3JLNBovNAw2SzQZZzQM+ks0GGM0DZKdzdGVwLTA5g6JjcJGCoXjNBWChectAYEzMzMzMzaFshKF4zQfNoXnNBYeld2lkdGjM+KZoZWlnaHQjoXDcABOSzQg+zQZEks0IUc0F0ZLNCHbNBQGSzQikzQRTks0Iw80D4JLNCMXNA8CSzQj5zQNUks0JIM0DBpLNCUXNAwKSzQlmzQKxks0Jl80COZLNCZLNAhOSzQmezQGSks0JoM0BfpLNCajNAXaSzQmezQFkks0JhM0BOJLNCVrNARaSzQktzP2nc3RlcC0xMIOiY3CRgqF4y0CIczMzMzMzoXnLQHVczMzMzM2hbISheM0FEKF5zQFrpXdpZHRo
 * zQECpmhlaWdodCOhcJqSzQHfzQTMks0BV80EWpLMls0DjZLM+s0C3JLNAX3NAfWSzQIBzQIDks0C+s0BqJLNBKnNAQmSzQbKzNeSzQfazMenc3RlcC0xMYKhbISheM0IaqF5zQGApXdpZHRozQEMpmhlaWdodCOhcJeSzQh2zQHdks0Ics0BxZLNCG3NAaqSzQhrzQGSks0IaM0BbpLNCGvNAUaSzQhwzQEjp3N0ZXAtMTKDomNwkYKheMtAeMMzMzMzM6F50LyhbISheM0Dp6F5zQJ9pXdpZHRozOumaGVpZ2h0I6FwmpLNCczNAXKSzQtizQMjks0Lds0D95LNC/bNBkSSzQwOzQaxks0MLc0G3pLNC/bNB0CSzQvKzQeNks0K6M0HzJLNCkbNB/Gnc3RlcC0xM4OiY3CRgqF4y0COmmZmZmZmoXnLwHOXAquvHiShbISheM0ItqF5zQcWpXdpZHRozQEXpmhlaWdodBOhcJSSzQn3
 * zQe6ks0KMc0HgZLNCnzNBzmSzQq3zQb/p3N0ZXAtMTSDomNwkYKheMtAjzwAAAAAAKF5y8BgdyjXKNcqoWyEoXjNBiShec0C66V3aWR0aM0BJaZoZWlnaHQjoXCUks0Kes0GnpLNCknNBp6SzQoRzQaeks0J1s0Gng==
 */
dynamic view voice_pipeline_advanced1 {
  title "Advanced Voice Pipeline Flow with Stages, Interruptions, Function Handling, Sentiment Analysis & Results Analysis"
  
  RT_room_server.RoomService -> Callevate "Stage 0: Subscriber connected"
  // Stage 1: Input Detection & Transcription
  // The agent listens to incoming speech and commits the transcript.  Callevate.Agent_system.VAD -> Callevate.Agent_system.EOU "Stage 1: Detects speech boundaries & signals start-of-speech"
  Callevate.Agent_system.EOU -> Callevate.Agent_system.STT_plug "Stage 1: Determines end-of-speech; commits transcript"
  
  // Stage 2: Sentiment Analysis & Processing
  // The transcribed text is first sent to the Sentiments Module for analysis, then forwarded to the LLM.
  Callevate.Agent_system.STT_plug -> Callevate.Agent_system.AgentFunctions.SentimentsModule "Stage 2: Analyzes sentiment of user input"
  Callevate.Agent_system.AgentFunctions.SentimentsModule -> Callevate.Agent_system.LLM_plug "Stage 2: Provides sentiment context with transcription"
  
  // Additional processing and function call handling
  Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.TTS_plug "Stage 2: Processes text & generates preliminary response"
  Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.AgentFunctions "Stage 2: Triggers function call requests"
  Callevate.Agent_system.AgentFunctions -> Callevate.Agent_system.LLM_plug "Stage 2: Returns function results for enhanced reply"
  
  // Stage 3: Interruption, Synthesis & Publishing
  // The agent handles interruptions, synthesizes the final response, and publishes audio.
  Callevate.Agent_system.VAD -> Callevate.Agent_system.Agent_Workflow "Stage 3: Triggers interruption if new speech is detected"
  Callevate.Agent_system.EOU -> Callevate.Agent_system.Agent_Workflow "Stage 3: Signals readiness for next round when end-of-speech detected"
  Callevate.Agent_system.TTS_plug -> Callevate.Agent_system.Agent_Workflow "Stage 3: Synthesizes and plays agent speech"
  Callevate.Agent_system -> RT_room_server.RoomService "Stage 3: Joins room & publishes synthesized audio"
  
  // Stage 4: Results Analysis
  // Once the call is finished, the room service forwards call data to the Call Results Analyser,
  // and analysis feedback is integrated back into the agent.
  RT_room_server.RoomService -> CallResultsAnalyser "Stage 4: Forwards call data for analysis"
  CallResultsAnalyser -> Callevate.Agent_system "Stage 4: Feeds analysis feedback back to the agent"
  
  autoLayout BottomTop 150 120
  style Callevate.Agent_system {
    border dashed
  }
}
}