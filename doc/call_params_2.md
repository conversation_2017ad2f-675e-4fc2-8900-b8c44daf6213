# ИнформацияОбАгенте

| **Поле**  | **Теги** | **Описание**                                         
|-----------|----------|------------------------------------------------------
| id        | core_api | Уникальный идентификатор агента.                     
| name      | prompt   | Имя агента, возможно полученное из `human_name`.     
| model     | openai   | Тип openai модели агента.                            
| profile   | prompt   | Имя профиля агента.                                  
| voiceId   | tts_api  | Идентификатор голоса агента.                         
| companyId | core_api | Идентификатор компании, к которой принадлежит агент. 
| MISSION   | prompt   | Детали, связанные с миссией.                         
| VOICE     | tts_api  | Детали, связанные с голосом.                         

### МИССИЯ

| **Поле**         | **Теги** | **Описание**                                                
|------------------|----------|-------------------------------------------------------------
| humanName        | prompt   | Имя агента, используемое в подсказках или генерации текста. 
| goal             | prompt   | Основная цель миссии.                                       
| offerDetails     | prompt   | Конкретные детали предложения, обсуждаемого в миссии.       
| langfusePromptId | prompt   | Идентификатор Langfuse (в настоящее время не используется). 

### ГОЛОС

| **Поле**      | **Теги**        | **Описание**                                                
|---------------|-----------------|-------------------------------------------------------------
| voiceId       | tts_api         | Идентификатор голоса.                                       
| name          | tts_api         | Имя, используемое в конструкторах (например, для `11labs`). 
| VOICESETTINGS | tts_api, prompt | Настройки, связанные с голосом.                             

#### НАСТРОЙКИГОЛОСА

| **Поле**          | **Теги** | **Описание**                                         
|-------------------|----------|------------------------------------------------------
| provider          | tts_api  | Поставщик голоса.                                    
| stability         | tts_api  | Стабильность вывода голоса.                          
| similarity_boost  | tts_api  | Уровень усиления сходства.                           
| style             | tts_api  | Стиль голоса.                                        
| use_speaker_boost | tts_api  | Использовать ли усиление громкоговорителя.           
| language          | tts_api  | Языки, используемые в голосе (список через запятую). 

---

# ОпцииСессии

| **Поле**          | **Теги**             | **Описание**                                                     
|-------------------|----------------------|------------------------------------------------------------------
| session_id        | logging_api          | Уникальный идентификатор сессии (например, GUID).                
| company_id        | core_api             | Идентификатор компании, участвующей в сессии.                    
| participant       | prompt               | Идентификатор участника.                                         
| conversation_id   | logging_api,core_api | Идентификатор для отслеживания разговора.                        
| campaign_id       | core_api             | Идентификатор, связанный с кампанией.                            
| conversation_type | core_api             | Тип разговора (например, `test-call`, `outbound-campaign-call`). 
| timezone          | prompt               | Часовой пояс сессии.                                             
| agent_id          | core_api             | Идентификатор агента, участвующего в сессии.                     
| room_name         | logging_api          | Название комнаты сессии (устарело).                              

# Дополнительньіе параметрьі для промпта

| **Поле**      | **Теги** | **Описание**      
|---------------|----------|-------------------
| comnany_name  | prompt   | Название компании 
| campaign_name | prompt   | Название кампейна 
