Prompting Guide
Introduction

What is prompt engineering?

Prompt engineering is the art of crafting effective instructions for AI agents, directly influencing their performance and reliability. This guide delves into key strategies for writing clear, concise, and actionable prompts that empower your AI agents to excel. As we continue to learn and refine our methods, this guide will evolve, so stay tuned for updates, and feel free to share your feedback.

Why is prompt engineering important?

Prompt engineering is crucial when building AI Agents because it determines how effectively the AI interprets and responds to user queries or tasks. Well-crafted prompts guide the model to produce accurate, relevant, and context-sensitive outputs, enabling it to better meet user needs. Poorly designed prompts can lead to ambiguous or incorrect results, limiting the agent’s utility.

How to measure success?

In the context of Voice AI Agents, we consider your “success rate” to be the percentage of requests your Agent manages to successfully handle from start to finish, without the intervention of a human.

The more complex your use case is, the more you will have to make experiments and iterate on your prompt to improve your success rate.

The process

When working with Voice AI Agents, following a structured approach ensures that your prompts produce accurate and meaningful results. Iterating through the steps of Design, Test, Refine, and Repeat allows for continuous improvement, making your interactions with the AI more effective and efficient. Here’s how to approach it:

Design: Start by carefully crafting your initial prompt, considering the specific task, context, and desired outcome. Clear and detailed prompts help guide the AI in understanding your needs.

Test: Run the prompt through the AI to see how it performs. Evaluate if the response aligns with your expectations and meets the intended goal. Testing helps identify potential gaps in clarity or structure.

Refine: Based on the results of the test, adjust the prompt to improve the response. This might involve rewording, adding more detail, or changing the phrasing to avoid ambiguity.

Repeat: Iterate on the process, testing the refined prompt and making further adjustments as needed. Each repetition improves the AI’s output, leading to more accurate and relevant responses over time. Your success rate (the amount of requests successfully handled by the agent) should improve accordingly.

General principles

Building Blocks of Effective Prompts: Sectional Organization

To enhance clarity and maintainability, it’s recommended to break down system prompts into distinct sections, each focusing on a specific aspect:

Identity: Define the persona and role of the AI agent, setting the tone for interactions.
Style: Establish stylistic guidelines, such as conciseness, formality, or humor, to ensure consistent communication.
Response Guidelines: Specify formatting preferences, question limits, or other structural elements for responses.
Task & Goals: Outline the agent’s objectives and the steps it should take to achieve them.
Example:

Task Breakdown: Step-by-Step Instructions

For complex interactions, breaking down the task into a sequence of steps enhances the agent’s understanding and ensures a structured conversation flow. Incorporate conditional logic to guide the agent’s responses based on user input. Example:

Controlling Response Timing

To prevent the agent from rushing through the conversation, explicitly indicate when to wait for the user’s response before proceeding to the next step.

Explicit Tool Integration

Specify when and how the agent should utilize external tools or APIs. Reference the tools by their designated names and describe their functions to ensure accurate invocation. Example:

Include Fallback and Error Handling Mechanisms

Always include fallback options and error-handling mechanisms in your prompts. This ensures that the Agent can gracefully handle unexpected user inputs or system errors.

Additional tips

Iterate as much as possible on your prompt. AI is driven by experimentation and iteration—refining prompts through trial and error will help you achieve more precise, relevant, and effective responses.
Use Markdown formatting: Using Markdown formatting in prompts is beneficial because it helps structure your content, making it clearer and more engaging for readers or AI models to understand.
Use Emotional Prompting: Emotional Prompting uses expressive language to shape a voice AI’s tone, creating more engaging and relatable responses. For example, instead of saying, “Tell me a story,” try, “Can you tell me a cozy bedtime story that’s warm and comforting?” This guides the AI to respond to the intended mood.
Add voice realism: To add voice realism in AI prompts, incorporate natural speech elements like stuttering, hesitations, and pauses.
Stuttering: Use repeated letters or sounds (e.g., “I-I-I don’t know” or “W-w-wait a second”).
Hesitations: Add fillers like “uh,” “um,” “well,” or “you know” (e.g., “I was, uh, thinking about it”).
Pauses: Use ellipses (”…”) or a series of periods (”…”) to indicate a pause (e.g., “I… I don’t know how to say this”).
Emotional emphasis: Use capital letters, exclamation marks, or ellipses to reflect tone or dramatic pauses (e.g., “I can’t… I just can’t believe it!” or “Well… that’s all, I guess.”).
Common issues

1. Numbers sound very robotic, how can we improve it?

When working with numbers that need to be expressed in writing, it’s often preferable to spell them out. This approach usually sounds more natural.

2. How to make assistants sound more human-like?

Add personality and provide tone to the assistant to make it sound more like a human being. Here are some examples:

Examples of great prompts

Appointment Setter

Additional resources

Check out these additional resources to learn more about prompt engineering:

learnprompting.org
promptingguide.ai
OpenAI’s guide to prompt engineering