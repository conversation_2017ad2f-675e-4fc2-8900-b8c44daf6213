import json
import re
from collections import Counter

import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import streamlit as st

# Configure the page
st.set_page_config(
    page_title="Filler Word Analysis Dashboard",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Set dark theme for plots
plt.style.use('dark_background')
sns.set_style('darkgrid')

# Function to tokenize text
def tokenize(text):
    return re.findall(r'\b\w+\b', text.lower())

# Function to analyze filler words
def analyze_filler_words(messages, fillers):
    filler_counts = Counter()
    for message in messages:
        tokens = tokenize(message)
        for filler in fillers:
            filler_counts[filler] += tokens.count(filler)
    return filler_counts

# Function to calculate percentages
def calculate_filler_percentage(filler_counts, total_count):
    return {filler: round((count / total_count) * 100, 2) if total_count > 0 else 0 for filler, count in filler_counts.items()}

# Function to plot total filler words
def plot_total_filler_words(filler_counts, dialogue_name):
    df = pd.DataFrame(list(filler_counts.items()), columns=['Filler Word', 'Count'])
    plt.figure(figsize=(10, 6))
    # Use a color palette suitable for dark background
    sns.barplot(data=df, x='Filler Word', y='Count', palette='rocket')
    plt.title(f"Total Filler Words in the Dialogue: {dialogue_name}", fontsize=14, color='white')
    plt.xlabel("Filler Words", fontsize=12, color='white')
    plt.ylabel("Count", fontsize=12, color='white')
    plt.xticks(rotation=45, color='white')
    plt.yticks(color='white')
    plt.tight_layout()
    st.pyplot(plt)
    plt.clf()

# Function to plot actor-specific percentages
def plot_actor_filler_percentages(agent_percentages, callee_percentages, dialogue_name):
    df_agent = pd.DataFrame(list(agent_percentages.items()), columns=['Filler Word', 'Agent Percentage'])
    df_callee = pd.DataFrame(list(callee_percentages.items()), columns=['Filler Word', 'Callee Percentage'])
    df = pd.merge(df_agent, df_callee, on='Filler Word')
    df_melted = df.melt(id_vars='Filler Word', var_name='Actor', value_name='Percentage')

    plt.style.use('dark_background')
    plt.figure(figsize=(12, 6))
    # Use a color palette suitable for dark background
    sns.barplot(data=df_melted, x='Filler Word', y='Percentage', hue='Actor', palette='bright')
    plt.title(f"Filler Word Usage by Actor in {dialogue_name} (Percentage of Total Words)", fontsize=14, color='white')
    plt.xlabel("Filler Words", fontsize=12, color='white')
    plt.ylabel("Percentage (%)", fontsize=12, color='white')
    plt.xticks(rotation=45, color='white')
    plt.yticks(color='white')
    legend = plt.legend(title='Actor')
    plt.setp(legend.get_texts(), color='white')
    plt.setp(legend.get_title(), color='white')
    plt.tight_layout()
    st.pyplot(plt)
    plt.clf()

# Main Streamlit app
def main():
    st.title("📊 Filler Word Analysis Dashboard")
    st.markdown("Analyze filler words in multiple dialogue data files to gain insights into speech patterns.")

    # Sidebar for file uploads and settings
    st.sidebar.header("Upload Files and Settings")
    uploaded_files = st.sidebar.file_uploader(
        "Upload JSON files with dialogue data",
        type="json",
        accept_multiple_files=True,
        help="You can upload multiple JSON files for analysis."
    )

    # Extended list of filler words
    default_filler_words = [
        "um", "uh", "like", "you know", "so", "well", "i mean", "oh",
        "hmm", "ah", "er", "actually", "basically", "literally", "right",
        "yeah", "you see", "got it", "sort of", "kind of", "okay", "alright"
    ]

    # Allow user to customize filler words
    with st.sidebar.expander("Customize Filler Words"):
        custom_fillers = st.text_area(
            "Enter additional filler words separated by commas:",
            help="You can add your own filler words to the analysis."
        )
        if custom_fillers:
            custom_filler_words = [word.strip() for word in custom_fillers.split(",") if word.strip()]
            extended_filler_words = default_filler_words + custom_filler_words
        else:
            extended_filler_words = default_filler_words

    if uploaded_files:
        # Initialize a list to collect analysis results
        results = []

        progress_bar = st.sidebar.progress(0)
        total_files = len(uploaded_files)

        for idx, uploaded_file in enumerate(uploaded_files):
            # Load the JSON data
            try:
                structured_data = json.load(uploaded_file)
            except json.JSONDecodeError:
                st.error(f"Error decoding JSON in file: {uploaded_file.name}")
                continue

            # Access the list of messages
            if isinstance(structured_data, dict) and "data" in structured_data:
                messages_list = structured_data["data"]
            elif isinstance(structured_data, list):
                messages_list = structured_data
            else:
                st.error(f"The JSON data in {uploaded_file.name} does not contain a 'data' key.")
                continue

            # Separate messages by actor
            agent_messages = [entry["message"] for entry in messages_list if entry.get("actor", "").lower() == "agent"]
            callee_messages = [entry["message"] for entry in messages_list if entry.get("actor", "").lower() == "callee"]

            # Analyze filler words
            total_messages = [entry["message"] for entry in messages_list]
            total_filler_counts = analyze_filler_words(total_messages, extended_filler_words)

            agent_filler_counts = analyze_filler_words(agent_messages, extended_filler_words)
            callee_filler_counts = analyze_filler_words(callee_messages, extended_filler_words)

            # Total word counts for agent and callee
            total_agent_words = sum(len(tokenize(message)) for message in agent_messages)
            total_callee_words = sum(len(tokenize(message)) for message in callee_messages)

            # Calculate percentages
            agent_filler_percentages = calculate_filler_percentage(agent_filler_counts, total_agent_words)
            callee_filler_percentages = calculate_filler_percentage(callee_filler_counts, total_callee_words)

            # Collect results
            dialogue_name = uploaded_file.name
            results.append({
                "Dialogue": dialogue_name,
                "Total Filler Words": sum(total_filler_counts.values()),
                "Agent Filler Words (Count)": sum(agent_filler_counts.values()),
                "Agent Filler Words (%)": round(sum(agent_filler_percentages.values()), 2),
                "Callee Filler Words (Count)": sum(callee_filler_counts.values()),
                "Callee Filler Words (%)": round(sum(callee_filler_percentages.values()), 2)
            })

            # Use expanders to organize content
            with st.expander(f"Results for {dialogue_name}", expanded=False):
                col1, col2 = st.columns(2)
                with col1:
                    st.subheader("Agent Filler Words (Counts)")
                    df_agent_counts = pd.DataFrame(agent_filler_counts.items(), columns=["Filler Word", "Count"])
                    st.table(df_agent_counts[df_agent_counts["Count"] > 0].reset_index(drop=True))
                with col2:
                    st.subheader("Callee Filler Words (Counts)")
                    df_callee_counts = pd.DataFrame(callee_filler_counts.items(), columns=["Filler Word", "Count"])
                    st.table(df_callee_counts[df_callee_counts["Count"] > 0].reset_index(drop=True))

                st.subheader("Agent vs. Callee Filler Word Percentages")
                plot_actor_filler_percentages(agent_filler_percentages, callee_filler_percentages, dialogue_name)

                st.subheader("Total Filler Words in the Dialogue")
                plot_total_filler_words(total_filler_counts, dialogue_name)

            # Update progress bar
            progress = (idx + 1) / total_files
            progress_bar.progress(progress)

        # Display aggregated results in a table
        st.header("📈 Aggregated Results")
        df_results = pd.DataFrame(results)
        st.dataframe(df_results.style.highlight_max(subset=["Total Filler Words", "Agent Filler Words (Count)", "Callee Filler Words (Count)"], color='lightgreen'))

        # Success message
        st.success("Analysis completed successfully!")

    else:
        st.info("Please upload at least one JSON file containing dialogue data.")

    # Footer
    st.markdown("---")
    st.markdown("Developed by [Your Name](https://yourwebsite.com)")

if __name__ == "__main__":
    main()