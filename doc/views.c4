@include "high-level.c4"

views {
  // System Context View: High-level view of the system and external entities
  view system_context {
    title 'System Context'
    include customer, Agent_system, CallResultsAnal<PERSON><PERSON>, Administrator, TelephonyProviders, RT_room_server
    style RT_room_server.* {
      display none
    }
  }

  // Container View: Shows containers within RT_room_server and external interactions
  /**
   * @likec4-generated(v1)
   * iKRoYXNo2ShjYjhmNzA0OTdkYjI4NjUyYTk3MGJkOTIyMDc0ZjQ5YTVmN2U3ZDI5qmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJUQqF4/KF50Lqld2lkdGjNB2GmaGVpZ2h0zQVdpW5vZGVzj6hjdXN0b21lcoKhYpT80LrNAUDMtKFjwq1BZG1pbmlzdHJhdG9ygqFilM0C/tC9zQFAzLShY8KpQ2FsbGV2YXRlgqFilM0CHcz3zQNNzQJkoWPDrlJUX3Jvb21fc2VydmVygqFilADNAd7NAbbNAjGhY8OxQUlfSW5mcmFzdHJ1Y3R1cmWCoWKUzQWjzJDNAbrNA0mhY8OyVGVsZXBob255UHJvdmlkZXJzgqFilBHMu80BQMy0oWPCtkNhbGxldmF0ZS5BZ2VudF9zeXN0ZW2CoWKUzQM3zQJ/zQFAzLShY8K6Q2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW2CoWKUzQJFzQEuzQL9zQEl
   * oWPDulJUX3Jvb21fc2VydmVyLlJvb21TZXJ2aWNlgqFilE7NAzPNAUDMtKFjwr9SVF9yb29tX3NlcnZlci5UZWxlcGhvbnlHYXRld2F5gqFilCjNAhvNAUDMtKFjwr9BSV9JbmZyYXN0cnVjdHVyZS5vbGxhbWFfc2VydmVygqFilM0Fy80C/c0BQMy0oWPC2SBBSV9JbmZyYXN0cnVjdHVyZS5FbGFic19zZXJ2aWNlc4KhYpTNBeTNAeXNAUDMtKFjwtkjQUlfSW5mcmFzdHJ1Y3R1cmUuZGVlcGdyYW1fc2VydmljZXOCoWKUzQX1zM3NAUDMtKFjwtkjQ2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW0uRnJvbnRlbmSCoWKUzQJtzQF3zQFAzLShY8K+Q2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW0uQVBJgqFilM0D2s0BZc0BQMy0oWPCpWVkZ2Vzi6Zsc2Nrb22DomNwkYKh
   * eMtAhbwAAAAAAKF5y0CI87RnFlXooWyEoXjNAmOhec0DM6V3aWR0aMzYpmhlaWdodBKhcJeSzQLfzQTlks0Cqc0EyJLNAm3NBKaSzQI4zQSEks0B8c0EVZLNAaTNBB2SzQFnzQPtpzFyZDkyc3mDomNwkYKheMy+oXnNAbKhbISheMy/oXnNAcWld2lkdGhgpmhlaWdodBKhcJSSzN3NAhySzOfNAe2SzPTNAbSSzP/NAYSmYnZzcHZvg6JjcJGCoXhroXnMmqFshKF4a6F5zJWld2lkdGhdpmhlaWdodBKhcJSSzQJOzJ2SzQIhzLCSzQHuzMWSzQHAzNmnMXFsbTU2c4OiY3CRgqF4zOChecyboWyEoXjM3qF5zJOld2lkdGhopmhlaWdodBKhcJSSzQG2zN2SzQHkzMqSzQIWzLWSzQJFzKGmeDQxcHRzgqJjcJGCoXjNBN2hectAiFglPIJTyKFwl5LNBB/NBQGSzQRvzQTiks0E
   * 0c0Et5LNBSHNBISSzQV6zQRKks0F080D+JLNBhHNA7imOHh2eHNngqJjcJGCoXjLQJOSAAAAAAChectAhONLHkWDLaFwmpLNBB/NBROSzQR1zQT4ks0E3M0Ey5LNBSHNBISSzQWnzQP6ks0FVM0DkZLNBcHNAvOSzQXWzQLVks0F8M0CuZLNBgrNAqCnMTFyYnQ3b4KiY3CRgqF4y0CTj1JYPkoWoXnLQIGWZmZmZmahcJqSzQQfzQUXks0Ed80E/ZLNBN/NBNCSzQUhzQSEks0F780Dl5LNBTrNAumSzQXazQHbks0F7M0BvZLNBgTNAaGSzQYdzQGIpzFidG50cWiComNwkYKheMtAkHLVg9ARbqF5y0CDAzMzMzMzoXCUks0DoM0E3pLNA8bNBHiSzQQDzQPSks0EKs0DaKZtY2R0NDmComNwkYKheMtAkNEqfC/ukqF5y0CBvMzMzMzNoXCUks0ELs0DXpLNBAjNA8SSzQPLzQRq
   * ks0DpM0E1KcxOTFuYXhvgqJjcJGCoXjLQI3UAAAAAAChectAfIcZ82AWcqFwlJLNA4LNA56SzQObzQOLks0Dts0Dd5LNA8/NA2SmdWN0Njk0g6JjcJGCoXjLQIs1hTZxhTahectAZ/MzMzMzM6FshKF4zQNWoXnM9KV3aWR0aMz0pmhlaWdodBKhcJSSzQM2zQHMks0DLc0CQ5LNAxzNAxeSzQMSzQOT
   */
  view container_view1 {
    title 'Container View'
    include RT_room_server.*, customer, Agent_system, AI_Infrastructure, TelephonyProviders
    include AI_Infrastructure.ollama_server, AI_Infrastructure.Elabs_services, AI_Infrastructure.deepgram_services
    include Callevate, Callevate.API, Callevate.Frontend, Administrator
    style RT_room_server.** {
      display none
    }
    style TelephonyProviders {
      shape rectangle
      color slate
    }
  }

  // Component View for TelephonyGateway: Internal components of TelephonyGateway
  view telephonygateway_component_view {
    title 'Telephony Gateway Component View'
    include RT_room_server.TelephonyGateway.*
    style RT_room_server.TelephonyGateway.** {
      display none
    }
  }

  // Component View for ManagementSystem: Internal components of ManagementSystem
  view managementsystem_component_view {
    title 'Management System Component View'
    include Callevate.ManagementSystem.*
    style Callevate.ManagementSystem.** {
      display none
    }
  }

  // Component View for Agent: Internal components of the Agent actor
  /**
   * @likec4-generated(v1)
   * iKRoYXNo2SgyZjc0NDk0YWYxMDk1N2M4Yzc1OGRlMzQwNTNhYTI2Yjc4NDM5MDMwqmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJUQqF40f9FoXnR/Zald2lkdGjNDRamaGVpZ2h0zQa5pW5vZGVz3gASrEFnZW50X3N5c3RlbYKhYpTR/0XR/s7NBKzNA9ahY8OpQ2FsbGV2YXRlgqFilPTR/ZbNAdDNAk6hY8OxQUlfSW5mcmFzdHJ1Y3R1cmWCoWKUzQQc0NXNAabNAtKhY8O7QWdlbnRfc3lzdGVtLkFnZW50RnVuY3Rpb25zgqFilNH/bSPNAvLNAduhY8O1QWdlbnRfc3lzdGVtLlRUU19wbHVngqFilM0CiTbNAUDMtKFjwrVBZ2VudF9zeXN0ZW0uU1RUX3BsdWeCoWKUzQKIzPzNAUDMtKFjwrVBZ2VudF9zeXN0ZW0uTExNX3BsdWeCoWKUzQKAzQHIzQFAzLShY8K7QWdlbnRf
   * c3lzdGVtLkFnZW50X1dvcmtmbG93gqFilM0ChtH/Bc0BQMy0oWPCukNhbGxldmF0ZS5NYW5hZ2VtZW50U3lzdGVtgqFilBzR/rPNAYDNAQmhY8O1Q2FsbGV2YXRlLkV4dGVybmFsQVBJgqFilC3R/c3NAUDMtKFjwtkgQUlfSW5mcmFzdHJ1Y3R1cmUuRWxhYnNfc2VydmljZXOCoWKUzQRaDM0BQMy0oWPC2SNBSV9JbmZyYXN0cnVjdHVyZS5kZWVwZ3JhbV9zZXJ2aWNlc4KhYpTNBEzM580BQMy0oWPCv0FJX0luZnJhc3RydWN0dXJlLm9sbGFtYV9zZXJ2ZXKCoWKUzQREzQHLzQFAzLShY8LZKUFnZW50X3N5c3RlbS5BZ2VudEZ1bmN0aW9ucy5zZW5kX2ZvbGxvd3VwgqFilNCYWs0BQMy0oWPC2S5BZ2VudF9zeXN0ZW0uQWdlbnRGdW5jdGlvbnMuc2NoZWR1bGVfY2Fs
   * bF9iYWNrgqFilMz3Ws0BQMy0oWPC2SdBZ2VudF9zeXN0ZW0uQWdlbnRGdW5jdGlvbnMuZG9fbm90X2NhbGyCoWKU0JXNASLNAUDMtKFjwtksQWdlbnRfc3lzdGVtLkFnZW50RnVuY3Rpb25zLlNlbnRpbWVudHNNb2R1bGWCoWKUzPfNARzNAUDMtKFjwr5DYWxsZXZhdGUuTWFuYWdlbWVudFN5c3RlbS5BUEmCoWKUPNH+6M0BQMy0oWPCpWVkZ2VzhqcxeGMxbW1ygqJjcJGCoXjLQI+kAAAAAAChectAVt73ve9736Fwl5LNBPTNASSSzQUGzQErks0FGc0BMZLNBSvNATaSzQZszQGWks0H780B1JLNCMjNAfKmYWYzbnBqgqJjcJGCoXjLQI98zMzMzM2hectAcqSETe7IRaFwmpLNBqLNASaSzQa0zQEtks0Gx80BMpLNBtnNATaSzQhXzQGQks0I18zvks0KSc0BcZLNCm3N
   * AX6SzQqRzQGTks0Ksc0BqacxbTVwa2hwgqJjcJGCoXjLQI88zMzMzM2hectAgAcLU9KwtaFwl5LNCFDM6pLNCVXM/5LNC1DNATCSzQv3zQFxks0MGs0Bf5LNDDzNAZOSzQxbzQGopnp0OGpqMYKiY3CRgqF4zQGpoXnLwGR6jZ31GzyhcJeSzQlDzQO5ks0JKs0D0pLNCQzNA+ySzQjuzQQBks0Iwc0EH5LNCIvNBDqSzQhZzQRPpzE4MDJxeGKComNwkYKheMtAarLwnzVe/KF5y8BzbmZmZmZmoXCUks0KMs0DX5LNClPNA1+SzQp1zQNfks0Kls0DX6cxbm54MzR3gqJjcJGCoXjLQGv/p3AWI/qhecvAFgAAAAAAAKFwmpLNA27NAhuSzQSQzQIzks0GuM0CY5LNCI/NAoqSzQikzQKMks0I280CiJLNCO7NApKSzQkbzQKqks0JQM0C1ZLNCVzNAvw=
   */
  view agent_component_view {
    title 'Agent Component View'
    include Agent_system.AgentFunctions
    include Agent_system.AgentFunctions.*
    include Agent_Workflow
    include Agent_system.LLM_plug
    include Agent_system.STT_plug
    include Agent_system.TTS_plug
    include AI_Infrastructure
    include AI_Infrastructure.Elabs_services
    include AI_Infrastructure.deepgram_services
    include ExternalAPI
    //include AI_Infrastructure.nemo_server
    include AI_Infrastructure.ollama_server
    include Callevate.API
    style Agent_system.AgentFunctions.SentimentsModule {
      shape rectangle
    }
    style AI_Infrastructure {
      border dotted
    }
    
  }

  // Component View for CallResultsAnalyser: Internal components of CallResultsAnalyser
  view callresultsanalyser_component_view {
    title 'Call Results Analyser Component View'
    include CallResultsAnalyser.*
    style CallResultsAnalyser.** {
      display none
    }
  }

  // Existing Landscape View (kept as is)
  /**
   * @likec4-generated(v1)
   * iKRoYXNo2ShiYTc2ODAyYTc4YjI5OGUwZTNiOGJiNTg0YzYxMDA5NGE0NGQ3YWNlqmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJMUqF40f0boXnQl6V3aWR0aM0GW6ZoZWlnaHTNCBClbm9kZXOIqGN1c3RvbWVygqFilNDQ0JfNAUDMtKFjwq1BZG1pbmlzdHJhdG9ygqFilNH9IzzNAUDMtKFjwqxBZ2VudF9zeXN0ZW2CoWKU0MzNAn7NAUDMtKFjwrJUZWxlcGhvbnlQcm92aWRlcnOCoWKU0MfMjM0BQMy0oWPCsUFJX0luZnJhc3RydWN0dXJlgqFilM0CNs0Cjc0BQMy0oWPCrlJUX3Jvb21fc2VydmVygqFilNDBzQFozQFAzLShY8KpQ2FsbGV2YXRlgqFilNH9G80Blc0BQMy0oWPCs0NhbGxSZXN1bHRzQW5hbHlzZXKCoWKU0f0bzQL0zQFAzLShY8KlZWRnZXOKpmJ2c3B2
   */
  view index {
    title 'Landscape view'
    style * {
      opacity 25%
    }
    include *
    autoLayout LeftRight
  }

  dynamic view voice_pipeline_view1 {
    title "Voice Pipeline Agent Flow"
    // Include the pipeline components from within the Agent_system in Callevate
    include Callevate.Agent_system.VAD, 
            Callevate.Agent_system.EOU, 
            Callevate.Agent_system.STT_plug, 
            Callevate.Agent_system.LLM_plug, 
            Callevate.Agent_system.TTS_plug

    // Define the relationships to illustrate the sequential processing flow:
    Callevate.Agent_system.VAD -> Callevate.Agent_system.EOU "Detects & refines end-of-utterance"
    Callevate.Agent_system.EOU -> Callevate.Agent_system.STT_plug "Passes audio for transcription"
    Callevate.Agent_system.STT_plug -> Callevate.Agent_system.LLM_plug "Converts speech to text & processes input"
    Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.TTS_plug "Generates response & converts to speech"
  }

  dynamic view voice_pipeline_full_view  {
    title 'Full Voice Pipeline Agent Flow with RT-room-server Interaction'
    // Include key components of the voice pipeline and the RT-room-server interaction
    include Callevate.Agent_system, 
            Callevate.Agent_system.VAD, 
            Callevate.Agent_system.EOU, 
            Callevate.Agent_system.STT_plug, 
            Callevate.Agent_system.LLM_plug, 
            Callevate.Agent_system.TTS_plug,
            RT_room_server.RoomService

    // The VoicePipelineAgent (represented by Agent_system) connects to the room
    Callevate.Agent_system -> RT_room_server.RoomService "Joins room & publishes audio track"

    // Internal voice pipeline flow:
    Callevate.Agent_system.VAD -> Callevate.Agent_system.EOU "Detects & refines end-of-utterance"
    Callevate.Agent_system.EOU -> Callevate.Agent_system.STT_plug "Passes audio for transcription"
    Callevate.Agent_system.STT_plug -> Callevate.Agent_system.LLM_plug "Converts speech to text & processes input"
    Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.TTS_plug "Generates response & converts to speech"
    
    autoLayout LeftRight 150 120
  }
  dynamic view voice_pipeline_advanced  {
    // Speech detection and end-of-utterance
    title "Advanced Voice Pipeline Flow with Interruptions & Function Handling"
    Callevate.Agent_system.VAD -> Callevate.Agent_system.EOU "Detects speech boundaries & signals start-of-speech"
    Callevate.Agent_system.EOU -> Callevate.Agent_system.STT_plug "Determines end-of-speech; commits transcript"
    
    // Transcription to language processing
    Callevate.Agent_system.STT_plug -> Callevate.Agent_system.LLM_plug "Converts audio to text & sends transcription"
    
    // LLM processing with potential function calls:
    Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.TTS_plug "Processes text & generates response"
    Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.AgentFunctions "Triggers function call requests"
    Callevate.Agent_system.AgentFunctions -> Callevate.Agent_system.LLM_plug "Returns function results for enhanced reply"
    
    // Interruption handling:
    Callevate.Agent_system.VAD -> Callevate.Agent_system.Agent_Workflow "Triggers interruption if new speech is detected"
    Callevate.Agent_system.EOU -> Callevate.Agent_system.Agent_Workflow "Triggers next round when end of speech detected"
    // Final speech synthesis and publishing:
    Callevate.Agent_system.TTS_plug -> Callevate.Agent_system.Agent_Workflow "Synthesizes and plays agent speech"
    Callevate.Agent_system -> RT_room_server.RoomService "Joins room & publishes synthesized audio"
    
    autoLayout lr 150 120
  }

  /**
 * @likec4-generated(v1)
 * iKRoYXNo2Sg1ZWEzOTgxODQ0ZTZjZDhmNDk5MWU2ZTBiOTIzZmQ2NGQ4NTI3MDAwqmF1dG9MYXlvdXSDqWRpcmVjdGlvbqJCVKdub2RlU2VweKdyYW5rU2VwzJaheMyWoXnR/rKld2lkdGjNC5emaGVpZ2h0zQk/pW5vZGVzjLpSVF9yb29tX3NlcnZlci5Sb29tU2VydmljZYKhYpTNAzrR/rLNAUDMtKFjwqlDYWxsZXZhdGWCoWKUzKz3zQjOzQUyoWPDs0NhbGxSZXN1bHRzQW5hbHlzZXKCoWKUzQU50f6yzQFAzLShY8K2Q2FsbGV2YXRlLkFnZW50X3N5c3RlbYKhYpTM1C7NCH7NBNOhY8O6Q2FsbGV2YXRlLkFnZW50X3N5c3RlbS5WQUSCoWKUzQRMzI/NAUDMtKFjwrpDYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkVPVYKhYpTM/M0Bvc0BQMy0oWPCv0NhbGxldmF0ZS5B
 * Z2VudF9zeXN0ZW0uU1RUX3BsdWeCoWKUzQI6zIDNAUDMtKFjwr9DYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkxMTV9wbHVngqFilM0DQs0EJc0BQMy0oWPCv0NhbGxldmF0ZS5BZ2VudF9zeXN0ZW0uVFRTX3BsdWeCoWKUzQfqzQHdzQFAzLShY8LZJUNhbGxldmF0ZS5BZ2VudF9zeXN0ZW0uQWdlbnRGdW5jdGlvbnOCoWKUzQOMzQHpzQGQzQEToWPD2SVDYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkFnZW50X1dvcmtmbG93gqFilM0H5GXNAUDMtKFjwtk2Q2FsbGV2YXRlLkFnZW50X3N5c3RlbS5BZ2VudEZ1bmN0aW9ucy5TZW50aW1lbnRzTW9kdWxlgqFilM0DtM0CIM0BQMy0oWPCpWVkZ2VzjqdzdGVwLTAxg6JjcJGCoXjLQIOuZmZmZmahecvAbZAAAAAAAKFshKF4zQgS
 * oXnNBpGld2lkdGjM2aZoZWlnaHQToXCUks0JRc0HupLNCSTNB5eSzQj8zQdvks0I1c0HR6dzdGVwLTAyg6JjcJGCoXjLQIpszMzMzM2hectAbTMzMzMzM6FshKF4zQGxoXnNA1ild2lkdGjNAQmmaGVpZ2h0IqFwlJLNB5DNBnWSzQZzzQYuks0EIs0FmpLNAv7NBVGnc3RlcC0wM4OiY3CRgqF4y0B1tmZmZmZmoXnLQHXeZmZmZmahbISheM0BlaF5zQE8pXdpZHRozPumaGVpZ2h0I6FwlJLNAlTNBMySzQJUzQSVks0CVM0ETJLNAlTNBBOnc3RlcC0wNIOiY3CRgqF4y0CFWzMzMzMzoXnLQG+ZmZmZmZqhbISheM0CK6F5zQLLpXdpZHRozPqmaGVpZ2h0I6FwmpLNAi7NA1WSzQIizQMsks0CH80C/ZLNAjvNAtySzQKDzQKGks0Czc0C3ZLNAzTNArGSzQNFzQKqks0DVc0C
 * oJLNA2XNApanc3RlcC0wNYOiY3CRgqF4y0CMjmZmZmZmoXnLQIXYzMzMzM2hbISheM0E3qF5zQLOpXdpZHRozQEXpmhlaWdodCOhcJqSzQRHzQKRks0EWc0CnJLNBGrNAqeSzQR8zQKxks0Ers0Cy5LNBMjNAriSzQTzzQLcks0FF80C+ZLNBTPNAySSzQVHzQNLp3N0ZXAtMDaDomNwkYKheMtAkp5mZmZmZqF5y0CLrmZmZmZmoWyEoXjNBy+hec0DOKV3aWR0aMz9pmhlaWdodCOhcJeSzQYPzQOQks0GgM0Dd5LNBxnNA0ySzQeTzQMJks0Hys0C65LNCADNAsCSzQgszQKYp3N0ZXAtMDeDomNwkYKheMtAisGZmZmZmqF5y0CLyzMzMzMzoWyEoXjNBBKhec0DWqV3aWR0aM0BDaZoZWlnaHQToXCXks0Ez80DnpLNBG7NA4uSzQP5zQNjks0Du80DCZLNA6vNAvKSzQOnzQLX
 * ks0Dqs0Cu6dzdGVwLTA4g6JjcJGCoXjLQIuuZmZmZmahectAhKTMzMzMzaFshKF4zQSAoXnNA5ald2lkdGjM+aZoZWlnaHQjoXCXks0EfM0CsZLNBOHNAteSzQYVzQKMks0GXs0C3JLNBovNAw2SzQZZzQM+ks0GGM0DZKdzdGVwLTA5g6JjcJGCoXjNBWChectAYEzMzMzMzaFshKF4zQfNoXnNBYeld2lkdGjM+KZoZWlnaHQjoXDcABOSzQg+zQZEks0IUc0F0ZLNCHbNBQGSzQikzQRTks0Iw80D4JLNCMXNA8CSzQj5zQNUks0JIM0DBpLNCUXNAwKSzQlmzQKxks0Jl80COZLNCZLNAhOSzQmezQGSks0JoM0BfpLNCajNAXaSzQmezQFkks0JhM0BOJLNCVrNARaSzQktzP2nc3RlcC0xMIOiY3CRgqF4y0CIczMzMzMzoXnLQHVczMzMzM2hbISheM0FEKF5zQFrpXdpZHRo
 * zQECpmhlaWdodCOhcJqSzQHfzQTMks0BV80EWpLMls0DjZLM+s0C3JLNAX3NAfWSzQIBzQIDks0C+s0BqJLNBKnNAQmSzQbKzNeSzQfazMenc3RlcC0xMYKhbISheM0IaqF5zQGApXdpZHRozQEMpmhlaWdodCOhcJeSzQh2zQHdks0Ics0BxZLNCG3NAaqSzQhrzQGSks0IaM0BbpLNCGvNAUaSzQhwzQEjp3N0ZXAtMTKDomNwkYKheMtAeMMzMzMzM6F50LyhbISheM0Dp6F5zQJ9pXdpZHRozOumaGVpZ2h0I6FwmpLNCczNAXKSzQtizQMjks0Lds0D95LNC/bNBkSSzQwOzQaxks0MLc0G3pLNC/bNB0CSzQvKzQeNks0K6M0HzJLNCkbNB/Gnc3RlcC0xM4OiY3CRgqF4y0COmmZmZmZmoXnLwHOXAquvHiShbISheM0ItqF5zQcWpXdpZHRozQEXpmhlaWdodBOhcJSSzQn3
 * zQe6ks0KMc0HgZLNCnzNBzmSzQq3zQb/p3N0ZXAtMTSDomNwkYKheMtAjzwAAAAAAKF5y8BgdyjXKNcqoWyEoXjNBiShec0C66V3aWR0aM0BJaZoZWlnaHQjoXCUks0Kes0GnpLNCknNBp6SzQoRzQaeks0J1s0Gng==
 */
}
