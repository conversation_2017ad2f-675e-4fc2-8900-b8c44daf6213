#!/bin/bash

# Default parameters
default_companyId="3dd09fe0-f892-4354-a4f2-9c94667186d1"
default_campaignId="acfeed64-7d08-485b-a72e-ce1155582a19"
default_conversationId="af7f39f6-6a22-4347-9aca-e8f9ad6b54ed"

# Function to generate a random room ID
generate_room_id() {
  uuidgen | tr '[:upper:]' '[:lower:]'
}

# Generate a unique room ID
unique_room_id=$(generate_room_id)

# Create the room name
room_name="call-_+380961717124_dCQhEKgEAoMc"

# Room metadata (JSON string)
#"stability": 0.4,
room_metadata='{"type":"inbound-campaign-call","analysis":{"prompt":""},"llm":{"name":"Emma","model":"gpt-4o","language":"eng","prompt":null,"profilePrompt":"A good girl and have to say me Hi <PERSON> at beginning"},"voice":{"name":"<PERSON><PERSON>","voiceId":"lSDyBIglyLP8FAMvutxO","provider":"11labs","vad_profile":"strict","similarity_boost":0.6,"style":0,"use_speaker_boost":true},"context":{"companyId":"62dd9340-4aed-4c08-93a4-bbdef08248b8","companyName":"local_bank","conversationId":"610c8d27-**************-7caf450c17f4","campaignId":"02de8981-622f-4a58-a9f2-f68bfc698995","timezone":"Asia/Dubai","participant":{"phoneNumber":"+************","firstName":"Mary","lastName":"Bee","email":"<EMAIL>","preferredLanguage":"ukr","customerSegment":"premium","lastContactDate":"2024-03-20","notes":"Interested in credit card offers"}}}'
lk room update --metadata "$room_metadata" "$room_name"


# cleanup call to web test
# lk --url https://livekit.callevate.ai --api-key API5Jc4NkMzTeVC --api-secret WpzOoSAxbp1cvegfz85fCZQhM8tXQrP9yYGcxacIWRIA room delete test-call-to-web:company-id:0ba9bad3-3d84-4135-b4dc-003fecfc0f3f:user-id:ed698ea1-0ef9-4eef-b020-866f5951d7da
