#!/bin/bash

# Specify the path to your .env file
ENV_FILE="../.env"

# Check if the .env file exists
if [ -f "$ENV_FILE" ]; then
  echo "[INFO]: Reading $ENV_FILE file."

  # Enable exporting all variables
  set -a

  # Source the .env file to load variables into the environment
  source "$ENV_FILE"

  # Disable automatic exporting
  set +a

  echo "[DONE]: Environment variables exported."
else
  echo "[ERROR]: $ENV_FILE not found."
fi
