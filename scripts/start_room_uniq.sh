#!/bin/bash

# Default parameters
default_companyId="3dd09fe0-f892-4354-a4f2-9c94667186d1"
default_campaignId="acfeed64-7d08-485b-a72e-ce1155582a19"
default_conversationId="af7f39f6-6a22-4347-9aca-e8f9ad6b54ed"

# Function to generate a random room ID
generate_room_id() {
  uuidgen | tr '[:upper:]' '[:lower:]'
}

# Generate a unique room ID
unique_room_id=$(generate_room_id)

# Create the room name
room_name="inbound-call:company-id:${default_companyId}:campaign-id:${default_campaignId}:conversation-id:${unique_room_id}"

# Room metadata (JSON string)
#"stability": 0.4,
room_metadata='{"type":"outbound-campaign-call","analysis":{"prompt":""},"llm":{"name":"<PERSON>","model":"gpt-4o","language":"eng","prompt":null,"profilePrompt":"A good girl and have to say me Hi <PERSON> at beginning"},"voice":{"name":"<PERSON><PERSON>","voiceId":"lSDyBIglyLP8FAMvutxO","provider":"11labs","vad_profile":"strict","similarity_boost":0.6,"style":0,"use_speaker_boost":true},"context":{"companyId":"62dd9340-4aed-4c08-93a4-bbdef08248b8","companyName":"local_bank","conversationId":"610c8d27-**************-7caf450c17f4","campaignId":"02de8981-622f-4a58-a9f2-f68bfc698995","timezone":"Asia/Dubai","participant":{"phoneNumber":"+************","firstName":"Mary","lastName":"Bee","email":"<EMAIL>","preferredLanguage":"ukr","customerSegment":"premium","lastContactDate":"2024-03-20","notes":"Interested in credit card offers"}}}'
output=$(lk token create --join --room "$room_name" --identity callee)


#lk dispatch create --agent-name jana-0.1 --room "$room_name" --metadata '{"user_id": "12345"}'

# Extract the token from the output using sed
access_token=$(echo "$output" | sed -n 's/.*Access token: \(.*\)/\1/p')

# Check if the access token was extracted
if [ -z "$access_token" ]; then
  echo "Failed to extract the access token."
  exit 1
fi

# Output the access token
echo "Access token: $access_token"

# Copy the access token to the clipboard (macOS command)
echo "$access_token" | pbcopy
echo "Access token copied to clipboard."

# Open browser with the token appended to the URL
url="http://localhost:3000/custom?liveKitUrl=ws://localhost:7880&token=$access_token"
echo "Opening browser with URL: $url"

# Open the URL in the default browser (macOS command)
open "$url"
a=1
sleep 2
lk room update --metadata "$room_metadata" "$room_name"


# cleanup call to web test
# lk --url https://livekit.callevate.ai --api-key API5Jc4NkMzTeVC --api-secret WpzOoSAxbp1cvegfz85fCZQhM8tXQrP9yYGcxacIWRIA room delete test-call-to-web:company-id:0ba9bad3-3d84-4135-b4dc-003fecfc0f3f:user-id:ed698ea1-0ef9-4eef-b020-866f5951d7da
