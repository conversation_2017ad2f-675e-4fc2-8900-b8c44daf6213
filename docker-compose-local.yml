services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
    environment:
      - ENV=development
      - LIVEKIT_URL=${LIVEKIT_URL}
      - LIVEKIT_API_KEY=${LIVEKIT_API_KEY}
      - LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_HOST=${LANGFUSE_HOST}
      - DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CORE_API_URL=${CORE_API_URL}
      - CORE_API_LOGIN=${CORE_API_LOGIN}
      - CORE_API_PASSWORD=${CORE_API_PASSWORD}
      - CONVERSATION_API_URL=${CONVERSATION_API_URL}
      - CONVERSATION_API_LOGIN=${CONVERSATION_API_LOGIN}
      - CONVERSATION_API_PASSWORD=${CONVERSATION_API_PASSWORD}
      - CALL_AGENT_ID=${CALL_AGENT_ID}
      - STT_PLUGIN=${STT_PLUGIN}
      - ASSEMBLYAI_API_KEY=${ASSEMBLYAI_API_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    networks:
      - call-agent
    env_file:
      - /Users/<USER>/Documents/ca-1.0/.docker_env

  redis:
    image: redis:7.2.4-alpine
    container_name: redis
    command: redis-server --requirepass call-agent
    ports:
      - '6379:6379'
    networks:
      - call-agent

  livekit-sip:
    container_name: livekit-sip
    image: livekit/sip:latest
    ports:
      - '5060:5060/udp'
      - '5060:5060/tcp'
      - '10000-10100:10000-10100/udp'
    environment:
      SIP_CONFIG_BODY: |
        api_key: 'APIeyFSC4YEbaWY'
        api_secret: '7PZRkOqnMrsJirBKT2vngLDQk6zdhf6xeMEIenOxnejC'
        ws_url: 'ws://livekit:7880'
        redis:
          address: 'redis:6379'
          password: 'call-agent'
        sip_port: 5060
        rtp_port: 10000-10100
        use_external_ip: true
        logging:
          level: debug
    networks:
      call-agent:


  livekit:
    container_name: livekit
    image: livekit/livekit-server:v1.8.0
    ports:
      - '30000-30100:30000-30100/udp'
      - '7881:7881/tcp'
      - '7880:7880'
    environment:
      LIVEKIT_KEYS: 'APIeyFSC4YEbaWY: 7PZRkOqnMrsJirBKT2vngLDQk6zdhf6xeMEIenOxnejC'
      REDIS_HOST: redis:6379
      REDIS_PASSWORD: call-agent
      UDP_PORT: 30000-30100
      LIVEKIT_NODE_IP: "0.0.0.0"
      LIVEKIT_CONFIG: |
        webhook:
          api_key: APIeyFSC4YEbaWY
          urls:
            - https://03ce-5-34-1-136.ngrok-free.app/v1/livekit/handler
        logging:
           level: debug
    networks:
      call-agent:

networks:
  call-agent:
    name: call-agent


