{"nodes": {"cachix": {"inputs": {"devenv": [], "flake-compat": [], "git-hooks": [], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1744206633, "narHash": "sha256-pb5aYkE8FOoa4n123slgHiOf1UbNSnKe5pEZC+xXD5g=", "owner": "cachix", "repo": "cachix", "rev": "8a60090640b96f9df95d1ab99e5763a586be1404", "type": "github"}, "original": {"owner": "cachix", "ref": "latest", "repo": "cachix", "type": "github"}}, "deploy-rs": {"inputs": {"flake-compat": ["flake-compat"], "nixpkgs": ["nixpkgs"], "utils": ["flake-utils"]}, "locked": {"lastModified": 1727447169, "narHash": "sha256-3KyjMPUKHkiWhwR91J1YchF6zb6gvckCAY1jOE+ne0U=", "owner": "<PERSON><PERSON><PERSON>", "repo": "deploy-rs", "rev": "aa07eb05537d4cd025e2310397a6adcedfe72c76", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON>", "repo": "deploy-rs", "type": "github"}}, "devenv": {"inputs": {"cachix": ["cachix"], "flake-compat": ["flake-compat"], "git-hooks": ["git-hooks"], "nix": ["devenv-nix"], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1745499686, "narHash": "sha256-eWL/LOcxS6xmaAmZMTNDJsfwn6G/BkxkKrhXJQ+PhAc=", "owner": "cachix", "repo": "devenv", "rev": "99f704634eebabe2528f5d562b23eb79d425426c", "type": "github"}, "original": {"owner": "cachix", "repo": "devenv", "type": "github"}}, "devenv-nix": {"inputs": {"flake-compat": ["flake-compat"], "flake-parts": ["flake-parts"], "libgit2": ["libgit2"], "nixpkgs": ["nixpkgs"], "nixpkgs-23-11": [], "nixpkgs-regression": [], "pre-commit-hooks": []}, "locked": {"lastModified": 1745500508, "narHash": "sha256-eUYh7+PgqLXTt8/9IOxEuW2qyxADECmTic8QNhEwKSw=", "owner": "<PERSON><PERSON><PERSON>", "repo": "nix", "rev": "090394819020afda8eae69e395b1accba9c0fab2", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON>", "ref": "devenv-2.24", "repo": "nix", "type": "github"}}, "devenv-root": {"flake": false, "locked": {"narHash": "sha256-d6xi4mKdjkX2JFicDIv5niSzpyI0m/Hnm8GGAIU04kY=", "type": "file", "url": "file:///dev/null"}, "original": {"type": "file", "url": "file:///dev/null"}}, "devenv-tmpdir": {"flake": false, "locked": {"narHash": "sha256-d6xi4mKdjkX2JFicDIv5niSzpyI0m/Hnm8GGAIU04kY=", "type": "file", "url": "file:///dev/null"}, "original": {"type": "file", "url": "file:///dev/null"}}, "dream2nix": {"inputs": {"nixpkgs": ["nixpkgs"], "purescript-overlay": ["purescript-overlay"], "pyproject-nix": ["pyproject-nix"]}, "locked": {"lastModified": 1735160684, "narHash": "sha256-n5CwhmqKxifuD4Sq4WuRP/h5LO6f23cGnSAuJemnd/4=", "owner": "nix-community", "repo": "dream2nix", "rev": "8ce6284ff58208ed8961681276f82c2f8f978ef4", "type": "github"}, "original": {"owner": "nix-community", "repo": "dream2nix", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1733328505, "narHash": "sha256-NeCCThCEP3eCl2l/+27kNNK7QrwZB1IJCrXfrbv5oqU=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "ff81ac966bb2cae68946d5ed5fc4994f96d0ffec", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-parts": {"inputs": {"nixpkgs-lib": ["nixpkgs"]}, "locked": {"lastModified": 1743550720, "narHash": "sha256-hIshGgKZCgWh6AYJpJmRgFdR3WUbkY04o82X05xqQiY=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "c621e8422220273271f52058f618c94e405bb0f5", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-utils": {"inputs": {"systems": ["systems"]}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "git-hooks": {"inputs": {"flake-compat": ["flake-compat"], "gitignore": ["gitignore"], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1742649964, "narHash": "sha256-DwOTp7nvfi8mRfuL1escHDXabVXFGT1VlPD1JHrtrco=", "owner": "cachix", "repo": "git-hooks.nix", "rev": "dcf5072734cb576d2b0c59b2ac44f5050b5eac82", "type": "github"}, "original": {"owner": "cachix", "repo": "git-hooks.nix", "type": "github"}}, "gitignore": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1709087332, "narHash": "sha256-HG2cCnktfHsKV0s4XW83gU3F57gaTljL9KNSuG6bnQs=", "owner": "hercules-ci", "repo": "gitignore.nix", "rev": "637db329424fd7e46cf4185293b9cc8c88c95394", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "gitignore.nix", "type": "github"}}, "libgit2": {"flake": false, "locked": {"lastModified": 1740952128, "narHash": "sha256-lmG/Vg3hxb/dRVPFhkQRB1dp8XT2YJzCvkPjm7yXP/A=", "owner": "libgit2", "repo": "libgit2", "rev": "21a351b0ed207d0871cb23e09c027d1ee42eae98", "type": "github"}, "original": {"owner": "libgit2", "repo": "libgit2", "type": "github"}}, "nix-update": {"inputs": {"flake-parts": ["flake-parts"], "nixpkgs": ["nixpkgs"], "treefmt-nix": ["treefmt-nix"]}, "locked": {"lastModified": 1745558947, "narHash": "sha256-trk20glViEO7j6S6gSroAr54EpX+0Z2KvU04sQb4blY=", "owner": "0x450x6c", "repo": "nix-update", "rev": "f9dacb6513a48ea9ed2fe5580e79bbb847ddb0c6", "type": "github"}, "original": {"owner": "0x450x6c", "repo": "nix-update", "type": "github"}}, "nix2container": {"inputs": {"flake-utils": ["flake-utils"], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1744699837, "narHash": "sha256-mJ1OgxMM2VTTjSVrMZItM8DxttzROYbWkmEPvYF/Kpg=", "owner": "nlewo", "repo": "nix2container", "rev": "78aadfc4ee1f9c2ee256e304b180ca356eb6a045", "type": "github"}, "original": {"owner": "nlewo", "repo": "nix2container", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1745554049, "narHash": "sha256-JBN1DamX3WTfDvwp4sBwIIt/nLJEEjBIvzUm2PIGVr0=", "owner": "NixOS", "repo": "nixpkgs", "rev": "ac88b73a8bf2621a593cd7dac1a9a525833e919b", "type": "github"}, "original": {"owner": "NixOS", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-stable": {"locked": {"lastModified": 1745487689, "narHash": "sha256-FQoi3R0NjQeBAsEOo49b5tbDPcJSMWc3QhhaIi9eddw=", "owner": "NixOS", "repo": "nixpkgs", "rev": "5630cf13cceac06cefe9fc607e8dfa8fb342dde3", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-24.11", "repo": "nixpkgs", "type": "github"}}, "onepunch-infra": {"inputs": {"flake-parts": ["flake-parts"], "nixpkgs": ["nixpkgs"], "nixpkgs-stable": ["nixpkgs-stable"]}, "locked": {"lastModified": 1745554733, "narHash": "sha256-TBLm9c5MKVFghLG8MJuf/tU8o0KcLX1jcIRx8+MKIsI=", "ref": "refs/heads/main", "rev": "1314f86ae3748dd94178103c3a58b9f51d141794", "revCount": 8, "type": "git", "url": "ssh://**************/MindesignGCV/onepunch-infra"}, "original": {"type": "git", "url": "ssh://**************/MindesignGCV/onepunch-infra"}}, "purescript-overlay": {"inputs": {"flake-compat": ["flake-compat"], "nixpkgs": ["nixpkgs"], "slimlock": ["slimlock"]}, "locked": {"lastModified": 1739795788, "narHash": "sha256-PG4lO49fvWHpQoCN3mIGflisXF+5vEf91ceV9hsWTLU=", "owner": "thoma<PERSON><PERSON><PERSON>", "repo": "purescript-overlay", "rev": "f754c2ef94cef46e5d5223c25d3f361e1f6cb509", "type": "github"}, "original": {"owner": "thoma<PERSON><PERSON><PERSON>", "repo": "purescript-overlay", "type": "github"}}, "pyproject-nix": {"flake": false, "locked": {"lastModified": 1702448246, "narHash": "sha256-hFg5s/hoJFv7tDpiGvEvXP0UfFvFEDgTdyHIjDVHu1I=", "owner": "davhau", "repo": "pyproject.nix", "rev": "5a06a2697b228c04dd2f35659b4b659ca74f7aeb", "type": "github"}, "original": {"owner": "davhau", "ref": "dream2nix", "repo": "pyproject.nix", "type": "github"}}, "root": {"inputs": {"cachix": "cachix", "deploy-rs": "deploy-rs", "devenv": "devenv", "devenv-nix": "devenv-nix", "devenv-root": "devenv-root", "devenv-tmpdir": "devenv-tmpdir", "dream2nix": "dream2nix", "flake-compat": "flake-compat", "flake-parts": "flake-parts", "flake-utils": "flake-utils", "git-hooks": "git-hooks", "gitignore": "gitignore", "libgit2": "libgit2", "nix-update": "nix-update", "nix2container": "nix2container", "nixpkgs": "nixpkgs", "nixpkgs-stable": "nixpkgs-stable", "onepunch-infra": "onepunch-infra", "purescript-overlay": "purescript-overlay", "pyproject-nix": "pyproject-nix", "slimlock": "slimlock", "systems": "systems", "treefmt-nix": "treefmt-nix"}}, "slimlock": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1705950978, "narHash": "sha256-N1/dGu/TaDeHy2emnb9gN2UJJnEuhZ4D3qUuWr2+534=", "owner": "thoma<PERSON><PERSON><PERSON>", "repo": "slimlock", "rev": "4c8d8f5e471065778957a60ba96913fad8dc1e79", "type": "github"}, "original": {"owner": "thoma<PERSON><PERSON><PERSON>", "repo": "slimlock", "type": "github"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "treefmt-nix": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1744961264, "narHash": "sha256-aRmUh0AMwcbdjJHnytg1e5h5ECcaWtIFQa6d9gI85AI=", "owner": "numtide", "repo": "treefmt-nix", "rev": "8d404a69efe76146368885110f29a2ca3700bee6", "type": "github"}, "original": {"owner": "numtide", "repo": "treefmt-nix", "type": "github"}}}, "root": "root", "version": 7}