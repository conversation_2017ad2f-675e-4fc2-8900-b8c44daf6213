{
  description = "call-agent agent";

  outputs = inputs @ {
    flake-parts,
    self,
    nixpkgs,
    ...
  }:
    flake-parts.lib.mkFlake {inherit inputs;} ({
      withSystem,
      flake-parts-lib,
      ...
    }: let
      inherit (flake-parts-lib) importApply;
      flakeModules.devenv = importApply ./nix/devenv/flake-module.nix {inherit withSystem;};
      flakeModules.call-agent-agent = importApply ./nix/call-agent-agent/flake-module.nix {
        inherit withSystem;
        localFlake = self;
      };
    in {
      imports = nixpkgs.lib.attrValues flakeModules;
      flake = {
        inherit flakeModules;
      };
      systems = [
        "x86_64-linux"
        "aarch64-linux"
        "aarch64-darwin"
      ];
      perSystem = {
        system,
        pkgs,
        lib,
        ...
      }: {
        formatter = pkgs.alejandra;
      };
    });

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs";
    nixpkgs-stable.url = "github:NixOS/nixpkgs/nixos-24.11";

    devenv-root = {
      url = "file+file:///dev/null";
      flake = false;
    };

    devenv-tmpdir = {
      url = "file+file:///dev/null";
      flake = false;
    };

    dream2nix = {
      url = "github:nix-community/dream2nix";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        purescript-overlay.follows = "purescript-overlay";
        pyproject-nix.follows = "pyproject-nix";
      };
    };

    treefmt-nix = {
      url = "github:numtide/treefmt-nix";
      inputs = {
        nixpkgs.follows = "nixpkgs";
      };
    };

    purescript-overlay = {
      url = "github:thomashoneyman/purescript-overlay";
      inputs = {
        flake-compat.follows = "flake-compat";
        nixpkgs.follows = "nixpkgs";
        slimlock.follows = "slimlock";
      };
    };

    onepunch-infra = {
      url = "git+ssh://**************/MindesignGCV/onepunch-infra";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        nixpkgs-stable.follows = "nixpkgs-stable";
        flake-parts.follows = "flake-parts";
      };
    };

    pyproject-nix = {
      url = "github:davhau/pyproject.nix/dream2nix";
      flake = false;
    };

    slimlock = {
      url = "github:thomashoneyman/slimlock";
      inputs = {
        nixpkgs.follows = "nixpkgs";
      };
    };

    deploy-rs = {
      url = "github:serokell/deploy-rs";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        utils.follows = "flake-utils";
        flake-compat.follows = "flake-compat";
      };
    };

    nix-update = {
      # url = "github:Mic92/nix-update";
      # see https://github.com/Mic92/nix-update/issues/357
      url = "github:0x450x6c/nix-update";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        flake-parts.follows = "flake-parts";
        treefmt-nix.follows = "treefmt-nix";
      };
    };

    flake-parts = {
      url = "github:hercules-ci/flake-parts";
      inputs = {
        nixpkgs-lib.follows = "nixpkgs";
      };
    };

    devenv = {
      url = "github:cachix/devenv";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        nix.follows = "devenv-nix";
        git-hooks.follows = "git-hooks";
        flake-compat.follows = "flake-compat";
        cachix.follows = "cachix";
      };
    };

    nix2container = {
      url = "github:nlewo/nix2container";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        flake-utils.follows = "flake-utils";
      };
    };

    flake-utils = {
      url = "github:numtide/flake-utils";
      inputs = {
        systems.follows = "systems";
      };
    };

    systems = {
      url = "github:nix-systems/default";
    };

    git-hooks = {
      url = "github:cachix/git-hooks.nix";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        gitignore.follows = "gitignore";
        flake-compat.follows = "flake-compat";
      };
    };

    gitignore = {
      url = "github:hercules-ci/gitignore.nix";
      inputs = {
        nixpkgs.follows = "nixpkgs";
      };
    };

    flake-compat = {
      url = "github:edolstra/flake-compat";
      flake = false;
    };

    devenv-nix = {
      url = "github:domenkozar/nix/devenv-2.24";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        flake-parts.follows = "flake-parts";
        flake-compat.follows = "flake-compat";
        pre-commit-hooks.follows = "";
        nixpkgs-23-11.follows = "";
        nixpkgs-regression.follows = "";
        libgit2.follows = "libgit2";
      };
    };

    libgit2 = {
      url = "github:libgit2/libgit2";
      flake = false;
    };

    cachix = {
      url = "github:cachix/cachix/latest";
      inputs = {
        nixpkgs.follows = "nixpkgs";
        flake-compat.follows = "";
        git-hooks.follows = "";
        devenv.follows = "";
      };
    };
  };
}
