// Tutorial reference:
// https://likec4.dev/tutorial/

specification {
  element system
  element actor
  element container
  element component
  {
    style {
        color sky
    }
  }
}

model {
  customer = actor 'Telephony Subscriber' {
     style { shape person
     color indigo
      }
    description 'The telephony subscriber'
  }
  web_customer = actor 'Web Subscriber' {
     style { shape person
     color indigo
      }
    description 'The web subscriber'
    
  }

  actor CallResultsAnalyser 'Call Results Analyser' {
    component AnalyseConversation 'Result Analyser'
    
  }

  actor Administrator 'Administrator' {
    style { 
        shape person 
        color indigo
    }
  }

  system customerSystem 'Customer System' {
    style { 
      shape rectangle
   }
    container sip_trunk 'SIP Trunk' {
      technology 'SIP'
      description 'SIP Trunk'
      -> TelephonyGateway.SIPTrunk

    }
    container ivr_service 'IVR Service' {
      technology 'IVR'
      description 'IVR Service'
    }
    container api 'Campaign-management-API' {
      technology 'REST'
      description 'Campaigns management API'
      component schedule_call 'Schedule Call'
      component receive_call_results 'Call Results Receiver'
      
    }
  }

  system Callevate ''{
    style {
        color sky
    }
   
     system Agent_system 'Agent-server'{
     description 'handles conversations worklow with functions, call analysis, sentiment detection'
     style {
            color sky
        }
    
    

    container Agent_instance 'Agent host'{
        description 'host 3 agents by default'
       
    }
    component AgentFunctions 'Agent Functions' {
      component send_followup 'Sends followup'
      component schedule_call_back 'Schedule Callback'
      component do_not_call 'Do not call'
      component SentimentsModule 'Sentiments Analyser'
       -> Callevate.API.SentimentsModule "Sentiment analysis"
    }
    component VAD 'Voice Auto Detection' {
      technology 'Silero VAD'
    }
    component EOU 'End of utterance detection'
    component STT_plug 'ASR and Transcription Plugin' {
      component stt_deepgram 'Deepgram Streaming'
      component stt_google 'OpenAI Whisper Files'
      component stt_nemo 'NVidia Nemo ASR Streaming' {
        description 'Experimental'
      }
    }
    component TTS_plug 'Voice synthesis plugin' {
      component tts_11labs 'ElevenLabs'
      component tts_cartesia 'Cartesia'
    }
    component LLM_plug 'LLM plugin' {
      component OpenAI_comp 'OpenAI API compatible'
      component ollama_comp 'Ollama compatible'
    }
    component Agent_Workflow 'Agent Workflow' {
      component Workflow_engine 'Configure dialog rules'
    }

  }
     container ManagementSystem 'Management System' {
      style { 
        shape rectangle
        color sky
      }

    container ExternalAPI_container 'External API' {
      style {
        color sky
      }
      description 'Calls management API, for Customer services'
      technology 'NodeJS'
      -> Database ''
      -> Campaings 'schedule calls'
      -> customerSystem.api.receive_call_results 'Call Results Callback'
    }
      component API 'Management API' {
        style {
            color sky
        }
        description 'Campaigns and Calls management API'
        technology 'NodeJS'
        -> Database ''
        -> ExternalAPI
        -> Agent_Workflow
      }
       component ExternalAPI 'External API' {
        style {
            color sky
        }
        description 'Calls management API, for Customer services'
        technology 'NodeJS'
        -> Database ''
        -> Agent_Workflow
        
      }
      component Frontend 'Management UI' {
        style {
            color sky
        }
        technology 'react, websockets'
        description 'Management Interface to manage agents, campaigns and calls'
        -> API ''
      }
        container Database 'Database' {
      description 'Stores room & participant data & conversations'
      technology 'PostgreSQL'
    }
    container ConversationLogDatabase 'Conversation Log Database' {
      description 'Stores Conversation logs'
      technology 'MongoDB'
    }
        -> CallResultsAnalyser
    }
   
    container Campaings 'Campaigns Run Service' {
      style { 
        shape rectangle
      }
    }
  }


  system AI_Infrastructure 'AI Infrastructure Servers'{
    style {
        color sky
    }
    container deepgram_services 'STT services'{
      technology 'NVidia Nemo'
      description 'Tuned Speech to Text services'
    }

    container Elabs_services 'TTS Services' {
      technology 'Coqui'
      description 'Tuned Text to Speech services'
    }
    container ollama_server 'OpenAI API compatible server' {
      technology 'Ollama, LLama '
      description 'conversation and analysis AI model'
    }

    container nemo_server 'Nemo ASR' {
      technology 'NVidia Nemo'
    }
    container openai_server 'OpenAI' {
      technology 'OpenAI'
    }

  }
 
 
  // Main system boundary
  system RT_room_server 'RT-room-server' {
    style { 
            color sky
        } 
    container RedisCache 'Cache' {
      technology 'Redis'
      description 'Stores room & participant data'
    }

    container WebRTC 'Web Gateway' {
      technology 'WebRTC'
      description 'Connects to web clients to RealTime Room server'
      -> RoomService
    }

    system  TelephonyGateway 'Telephony Gateway' {
      description 'Bridges PSTN/SIP'
      style {
        
      }
      component SIPTrunk 'SIP Trunk' {
        description 'SIP'
        -> customerSystem.sip_trunk
      }
    
      -> RoomService
      component DTMFServices 'DTMF Services' {
        description 'DTMF services'
        -> RoomService
      }
    }
    // Containers
   
    container RoomService 'Room Service' {
      description 'Manages room lifecycle & config'
      technology 'gRPC protocol'
      style{
       
      }
      -> RedisCache '-'
    }
   
  }

  // Relationships
  Agent_system -> RT_room_server.RoomService '-'
  RT_room_server.TelephonyGateway -> customerSystem.sip_trunk 'SIP integration'
  RT_room_server.RoomService -> Callevate.Database 'Reads/writes config and conversations state'
  Administrator -> Frontend 'Manages configuration and campaigns'
  customer -> customerSystem.ivr_service 'Incoming calls'
  customerSystem.ivr_service -> customer 'Outcoming calls'
  // Added relationship for incoming calls from TelephonyProviders
  
  //Agent_system.LLM_plug.OpenAI_comp -> AI_Infrastructure.openai_server
  Agent_system.LLM_plug.ollama_comp -> AI_Infrastructure.ollama_server
  Agent_system.STT_plug.stt_deepgram -> AI_Infrastructure.deepgram_services
  Agent_system.TTS_plug.tts_11labs -> AI_Infrastructure.Elabs_services
  //Agent_system.STT_plug.stt_nemo -> AI_Infrastructure.nemo_server

  customerSystem.api.schedule_call -> Callevate.ExternalAPI_container "schedule call"


views {
  // System Context View: High-level view of the system and external entities
  view system_context {
    title 'System Context'
    include customer, Agent_system, CallResultsAnalyser, Administrator, TelephonyProviders, RT_room_server
    style RT_room_server.* {
      display none
    }
  }

  // Container View: Shows containers within RT_room_server and external interactions
  /**
   * @likec4-generated(v1)
   * iKRoYXNo2SgzYjNhYjI2Nzc4MjM4YWJhZDYwMzFhNmJmZTdmYzA5ZjljMDlhZWQ0qmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJUQqF4AKF5AKV3aWR0aM0JPaZoZWlnaHTNBQelbm9kZXOPqGN1c3RvbWVygqFilM0HsADNAUDMtKFjwq5jdXN0b21lclN5c3RlbYKhYpTNB73NAoTNAYDNAQmhY8OpQ2FsbGV2YXRlgqFilM0FW80Bes0B+80DjaFjw65SVF9yb29tX3NlcnZlcoKhYpQAzQJLzQLUzQIMoWPDsUFJX0luZnJhc3RydWN0dXJlgqFilM0DOM0Bb80Bv80DSaFjw7JjdXN0b21lclN5c3RlbS5hcGmCoWKUzQfdzQK5zQFAzLShY8K6Q2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW2CoWKUzQWDzQLYzQGrzQIHoWPDtkNhbGxldmF0ZS5BZ2VudF9zeXN0ZW2CoWKUzQWp
   * zQGxzQFazLShY8K/UlRfcm9vbV9zZXJ2ZXIuVGVsZXBob255R2F0ZXdheYKhYpTNAWzNAojNAUDMtKFjwrpSVF9yb29tX3NlcnZlci5Sb29tU2VydmljZYKhYpQozQN7zQFAzLShY8K/QUlfSW5mcmFzdHJ1Y3R1cmUub2xsYW1hX3NlcnZlcoKhYpTNA4/NAsTNAUDMtKFjwtkgQUlfSW5mcmFzdHJ1Y3R1cmUuRWxhYnNfc2VydmljZXOCoWKUzQN3zQGszQFAzLShY8LZI0FJX0luZnJhc3RydWN0dXJlLmRlZXBncmFtX3NlcnZpY2VzgqFilM0DYc0D3M0BQMy0oWPCvkNhbGxldmF0ZS5NYW5hZ2VtZW50U3lzdGVtLkFQSYKhYpTNBbzNAw7NAUDMtKFjwtkwQ2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW0uRXh0ZXJuYWxBUElfY29udGFpbmVygqFilM0Fq80EA80BW8y0
   * oWPCpWVkZ2VziKcxbjY5anBrgaFwlJLNAZXNAzySzQF8zQNPks0BYc0DYpLNAUjNA3WmbHNja29tg6JjcJSCoXjNBZChec0BioKheM0Ez6F5zQFcgqF4zQItoXnNAceCoXjNARShec0C96FshKF4zQLooXnNAYild2lkdGgJpmhlaWdodBKhcJ2SzQYVzQG7ks0Fw80BmZLNBV/NAXaSzQUBzQFlks0ENc0BQZLNA/bNAS2SzQMuzQFlks0CSM0BppLNAgHNAcuSzQFizQJ+ks0BJM0CxZLM+80DKJLM480DcqZ4NDFwdHOComNwkYKheM0FdqF5zQKWoXCUks0GFc0CVJLNBbPNAnySzQU5zQKvks0E2M0C2KY4eHZ4c2eComNwkYKheM0Fa6F5zQIJoXCUks0GFc0CCpLNBa3NAgmSzQUpzQIIks0Ewc0CCKcxMXJidDdvgqJjcJOCoXjNBcWhec0CiYKheM0FIaF5zQMmgqF4zQSs
   * oXnNA62hcJqSzQYVzQJaks0F4M0CdpLNBajNApmSzQV5zQK/ks0FIs0DB5LNBSjNAzOSzQTZzQOCks0EvM0Dn5LNBJzNA7ySzQR8zQPWpzFrMDFxdnKDomNwkYKheM0IEaF5zQPeoWyEoXjNB9Chec0D/KV3aWR0aFWmaGVpZ2h0EqFwlJLNCQ/NA5WSzQh+zQO/ks0HqM0D/JLNBxDNBCinMTY5dWI3YoOiY3CRgqF4zQdioXnNBBChbISheM0HnqF5zQPkpXdpZHRozIimaGVpZ2h0EqFwlJLNBwbNBCuSzQebzQQAks0Icc0DwpLNCQXNA5imbWNkdDQ5gqJjcJGCoXjNBzKhec0Cu6FwlJLNB2LNAweSzQdEzQLYks0HH80CnpLNBwHNAm4=
   */
  view container_view {
    title 'Container View'
    include RT_room_server, RT_room_server.TelephonyGateway, RT_room_server.RoomService, customer, Agent_system, AI_Infrastructure,
    include AI_Infrastructure.ollama_server, AI_Infrastructure.Elabs_services, AI_Infrastructure.deepgram_services
    include customerSystem, customerSystem.api
    include Callevate.ExternalAPI_container
  
    include Callevate, Callevate.API
    style RT_room_server.** {
      display none
    }
    

   /**
    * @likec4-generated(v1)
    * iKRoYXNo2SgzMWM1M2YzNmJiODY5YjFmNDEzZjNiZjAzOGRjMzVkMzE4ZDRhMWZlqmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJUQqF4CKF59aV3aWR0aM0LUqZoZWlnaHTNBSilbm9kZXPeABSuY3VzdG9tZXJTeXN0ZW2CoWKUzQYBzQFRzQLWzQHtoWPDrUFkbWluaXN0cmF0b3KCoWKUzQITGM0BQMy0oWPCqGN1c3RvbWVygqFilM0FwfXNAUDMtKFjwqlDYWxsZXZhdGWCoWKUK80BLc0FWM0CjaFjw65SVF9yb29tX3NlcnZlcoKhYpTNBZzNA4HNA17NAW6hY8OxQUlfSW5mcmFzdHJ1Y3R1cmWCoWKUCM0EBM0E7M0BGaFjw7pjdXN0b21lclN5c3RlbS5pdnJfc2VydmljZYKhYpTNB2/NAYvNAUDMtKFjwrJjdXN0b21lclN5c3RlbS5hcGmCoWKUzQYnzQGFzQFAzLShY8K4
    * Y3VzdG9tZXJTeXN0ZW0uc2lwX3RydW5rgqFilM0G6M0CYs0BQMy0oWPCukNhbGxldmF0ZS5NYW5hZ2VtZW50U3lzdGVtgqFilFPNAVXNBQjNARmhY8O2Q2FsbGV2YXRlLkFnZW50X3N5c3RlbYKhYpRuzQLezQFazLShY8K/UlRfcm9vbV9zZXJ2ZXIuVGVsZXBob255R2F0ZXdheYKhYpTNB1LNA77NAYDNAQmhY8O6UlRfcm9vbV9zZXJ2ZXIuUm9vbVNlcnZpY2WCoWKUzQXEzQPzzQFAzLShY8K/QUlfSW5mcmFzdHJ1Y3R1cmUub2xsYW1hX3NlcnZlcoKhYpQwzQRBzQFAzLShY8LZIEFJX0luZnJhc3RydWN0dXJlLkVsYWJzX3NlcnZpY2VzgqFilM0B3s0EQc0BQMy0oWPC2SNBSV9JbmZyYXN0cnVjdHVyZS5kZWVwZ3JhbV9zZXJ2aWNlc4KhYpTNA4zNBEHNAUDMtKFj
    * wtkwQ2FsbGV2YXRlLk1hbmFnZW1lbnRTeXN0ZW0uRXh0ZXJuYWxBUElfY29udGFpbmVygqFilM0D180Bks0BW8y0oWPC2SNDYWxsZXZhdGUuTWFuYWdlbWVudFN5c3RlbS5Gcm9udGVuZIKhYpTNAinNAZLNAUDMtKFjwr5DYWxsZXZhdGUuTWFuYWdlbWVudFN5c3RlbS5BUEmCoWKUe80Bks0BQMy0oWPC2ShSVF9yb29tX3NlcnZlci5UZWxlcGhvbnlHYXRld2F5LlNJUFRydW5rgqFilM0Hcs0D880BQMy0oWPCpWVkZ2VzjqcxbTJiZTZtg6JjcJGCoXjLQJ2ffNjoChehectAdHMzMzMzM6FshKF4zQc4oXnNARqld2lkdGhopmhlaWdodBKhcJeSzQrqzQN0ks0K2M0DjJLNCs3NA6mSzQrVzQPHks0K4c0D75LNCvfNBBeSzQsNzQQ5pmpldjRlbYOiY3CRgqF4y0CcIIMn
    * F/XpoXnLQG2ZmZmZmZqhbISheM0HOKF5zQEapXdpZHRoXaZoZWlnaHQSoXCUks0LWs0EQZLNC1rNBAeSzQtazQO7ks0LWs0DfqZ4NDFwdHOComNwkYKheMtAb5yepdvxlKF5y0CONAAAAAAAoXCXks0CHM0DdJLNAfbNA5CSzQHMzQOwks0Bp80Dz5LNAX/NA/CSzQFUzQQXks0BMM0EOqY4eHZ4c2eComNwkYKheMtAepgAAAAAAKF5y0CONAAAAAAAoXCUks0Clc0DdJLNApDNA66SzQKKzQP7ks0Chs0EN6cxMXJidDdvgqJjcJGCoXjLQIJ8zMzMzM2hectAjhx2tKriU6FwlJLNAvnNA3SSzQM3zQOvks0Dic0D/pLNA8jNBDumbWNkdDQ5gqJjcJGCoXjNARuhectAg5zMzMzMzaFwlJLNApzNAiiSzQKczQJUks0CnM0CiZLNApzNAranMTkxbmF4b4KiY3CRgqF4zQIIoXnN
    * AeyhcJSSzQOqzQHOks0Dic0BzpLNA2jNAc6SzQNGzQHOpzFrMDFxdnKDomNwkYKheMtAlzhmZmZmZqF5y0B5AymhskHIoWyEoXjNBayhec0BlKV3aWR0aFWmaGVpZ2h0EqFwmpLNCUbNAsCSzQkkzQKoks0I/c0CkJLNCNXNAoOSzQitzQJ1ks0IP80CgpLNCBXNAnuSzQegzQJmks0HIc0CPZLNBr3NAhinMTY5dWI3YoOiY3CRgqF4y0CV05mZmZmaoXnLQHqQrPd9WtqhbISheM0FraF5zQGupXdpZHRozIimaGVpZ2h0EqFwl5LNBrTNAeOSzQdLzQH5ks0IKM0CKJLNCNXNAoOSzQjzzQKTks0JE80CppLNCS/NArqmdWN0Njk0g6JjcJGCoXjLQIXdj9j9j9mhectAcHZmZmZmZqFshKF4zQK+oXnNAS+ld2lkdGjM9KZoZWlnaHQSoXCUks0ESsy0ks0ESszqks0ESs0BMZLN
    * BErNAWqmbHNja29tg6JjcJGCoXjLQIa0zMzMzMyhectAjbwZtCH+wqFshKF4zQPDoXnNA+Kld2lkdGgJpmhlaWdodBKhcJeSzQNJzQM2ks0DzM0DUJLNBIHNA3+SzQUPzQPPks0FQc0D65LNBXHNBBSSzQWYzQQ6png1cThhdYKiY3CRgqF4y0CePgM06QTPoXnLQIt9mZmZmZqhcJeSzQfizQN0ks0H3M0DiJLNB9bNA5ySzQfRzQOwks0Hxc0D3JLNB7rNBA2SzQexzQQ3pm8xMHZiYYKiY3CRgqF4y0CeUfzLFvsxoXnLQI86ZmZmZmahcJSSzQezzQRBks0Hws0EB5LNB9XNA7qSzQflzQN+pzFuNjlqcGuComNwkYKheM0GuqF5zQSboXCUks0G3M0Em5LNBsXNBJuSzQavzQSbks0GmM0Emw==
    */
   view container_view_v_1_1 {
    title 'Container View'
    include RT_room_server, customerSystem.*, customer, Agent_system, AI_Infrastructure
    include AI_Infrastructure.ollama_server, AI_Infrastructure.Elabs_services, AI_Infrastructure.deepgram_services
    include Callevate, Callevate.API, Callevate.Frontend, Callevate.ExternalAPI_container, Administrator
    include RT_room_server.RoomService, RT_room_server.TelephonyGateway
    include  SIPTrunk
    style RT_room_server.** {
    }
    style customerSystem {
      shape rectangle
      color slate
    }
     style RT_room_server {
       border solid
     }
  }

  // Component View for TelephonyGateway: Internal components of TelephonyGateway
  view telephonygateway_component_view {
    title 'Telephony Gateway Component View'
    include RT_room_server.TelephonyGateway.*
    style RT_room_server.TelephonyGateway.** {
      display none
    }
  }

  // Component View for ManagementSystem: Internal components of ManagementSystem
  view managementsystem_component_view {
    title 'Management System Component View'
    include Callevate.ManagementSystem.*
    style Callevate.ManagementSystem.** {
      display none
    }
  }

  // Component View for Agent: Internal components of the Agent actor

  /**
   * @likec4-generated(v1)
   * iKRoYXNo2SgyZjc0NDk0YWYxMDk1N2M4Yzc1OGRlMzQwNTNhYTI2Yjc4NDM5MDMwqmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJUQqF40f9FoXnR/Zald2lkdGjNDRamaGVpZ2h0zQa5pW5vZGVz3gASrEFnZW50X3N5c3RlbYKhYpTR/0XR/s7NBKzNA9ahY8OpQ2FsbGV2YXRlgqFilPTR/ZbNAdDNAk6hY8OxQUlfSW5mcmFzdHJ1Y3R1cmWCoWKUzQQc0NXNAabNAtKhY8O7QWdlbnRfc3lzdGVtLkFnZW50RnVuY3Rpb25zgqFilNH/bSPNAvLNAduhY8O1QWdlbnRfc3lzdGVtLlRUU19wbHVngqFilM0CiTbNAUDMtKFjwrVBZ2VudF9zeXN0ZW0uU1RUX3BsdWeCoWKUzQKIzPzNAUDMtKFjwrVBZ2VudF9zeXN0ZW0uTExNX3BsdWeCoWKUzQKAzQHIzQFAzLShY8K7QWdlbnRf
   * c3lzdGVtLkFnZW50X1dvcmtmbG93gqFilM0ChtH/Bc0BQMy0oWPCukNhbGxldmF0ZS5NYW5hZ2VtZW50U3lzdGVtgqFilBzR/rPNAYDNAQmhY8O1Q2FsbGV2YXRlLkV4dGVybmFsQVBJgqFilC3R/c3NAUDMtKFjwtkgQUlfSW5mcmFzdHJ1Y3R1cmUuRWxhYnNfc2VydmljZXOCoWKUzQRaDM0BQMy0oWPC2SNBSV9JbmZyYXN0cnVjdHVyZS5kZWVwZ3JhbV9zZXJ2aWNlc4KhYpTNBEzM580BQMy0oWPCv0FJX0luZnJhc3RydWN0dXJlLm9sbGFtYV9zZXJ2ZXKCoWKUzQREzQHLzQFAzLShY8LZKUFnZW50X3N5c3RlbS5BZ2VudEZ1bmN0aW9ucy5zZW5kX2ZvbGxvd3VwgqFilNCYWs0BQMy0oWPC2S5BZ2VudF9zeXN0ZW0uQWdlbnRGdW5jdGlvbnMuc2NoZWR1bGVfY2Fs
   * bF9iYWNrgqFilMz3Ws0BQMy0oWPC2SdBZ2VudF9zeXN0ZW0uQWdlbnRGdW5jdGlvbnMuZG9fbm90X2NhbGyCoWKU0JXNASLNAUDMtKFjwtksQWdlbnRfc3lzdGVtLkFnZW50RnVuY3Rpb25zLlNlbnRpbWVudHNNb2R1bGWCoWKUzPfNARzNAUDMtKFjwr5DYWxsZXZhdGUuTWFuYWdlbWVudFN5c3RlbS5BUEmCoWKUPNH+6M0BQMy0oWPCpWVkZ2VzhqcxeGMxbW1ygqJjcJGCoXjLQI+kAAAAAAChectAVt73ve9736Fwl5LNBPTNASSSzQUGzQErks0FGc0BMZLNBSvNATaSzQZszQGWks0H780B1JLNCMjNAfKmYWYzbnBqgqJjcJGCoXjLQI98zMzMzM2hectAcqSETe7IRaFwmpLNBqLNASaSzQa0zQEtks0Gx80BMpLNBtnNATaSzQhXzQGQks0I18zvks0KSc0BcZLNCm3N
   * AX6SzQqRzQGTks0Ksc0BqacxbTVwa2hwgqJjcJGCoXjLQI88zMzMzM2hectAgAcLU9KwtaFwl5LNCFDM6pLNCVXM/5LNC1DNATCSzQv3zQFxks0MGs0Bf5LNDDzNAZOSzQxbzQGopnp0OGpqMYKiY3CRgqF4zQGpoXnLwGR6jZ31GzyhcJeSzQlDzQO5ks0JKs0D0pLNCQzNA+ySzQjuzQQBks0Iwc0EH5LNCIvNBDqSzQhZzQRPpzE4MDJxeGKComNwkYKheMtAarLwnzVe/KF5y8BzbmZmZmZmoXCUks0KMs0DX5LNClPNA1+SzQp1zQNfks0Kls0DX6cxbm54MzR3gqJjcJGCoXjLQGv/p3AWI/qhecvAFgAAAAAAAKFwmpLNA27NAhuSzQSQzQIzks0GuM0CY5LNCI/NAoqSzQikzQKMks0I280CiJLNCO7NApKSzQkbzQKqks0JQM0C1ZLNCVzNAvw=
   */
  view agent_component_view {
    title 'Agent Component View'
    include Agent_system.AgentFunctions
    include Agent_system.AgentFunctions.*
    include Agent_Workflow
    include Agent_system.LLM_plug
    include Agent_system.STT_plug
    include Agent_system.TTS_plug
    include AI_Infrastructure
    include AI_Infrastructure.Elabs_services
    include AI_Infrastructure.deepgram_services
    include ExternalAPI
    //include AI_Infrastructure.nemo_server
    include AI_Infrastructure.ollama_server
    include Callevate.API
    style Agent_system.AgentFunctions.SentimentsModule {
      shape rectangle
    }
    style AI_Infrastructure {
      border dotted
    }
    
  }

  // Component View for CallResultsAnalyser: Internal components of CallResultsAnalyser
  view callresultsanalyser_component_view {
    title 'Call Results Analyser Component View'
    include CallResultsAnalyser.*
    style CallResultsAnalyser.** {
      display none
    }
  }

  // Existing Landscape View (kept as is)
  /**
   * @likec4-generated(v1)
   * iKRoYXNo2ShiYTc2ODAyYTc4YjI5OGUwZTNiOGJiNTg0YzYxMDA5NGE0NGQ3YWNlqmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJMUqF40f0boXnQl6V3aWR0aM0GW6ZoZWlnaHTNCBClbm9kZXOIqGN1c3RvbWVygqFilNDQ0JfNAUDMtKFjwq1BZG1pbmlzdHJhdG9ygqFilNH9IzzNAUDMtKFjwqxBZ2VudF9zeXN0ZW2CoWKU0MzNAn7NAUDMtKFjwrJUZWxlcGhvbnlQcm92aWRlcnOCoWKU0MfMjM0BQMy0oWPCsUFJX0luZnJhc3RydWN0dXJlgqFilM0CNs0Cjc0BQMy0oWPCrlJUX3Jvb21fc2VydmVygqFilNDBzQFozQFAzLShY8KpQ2FsbGV2YXRlgqFilNH9G80Blc0BQMy0oWPCs0NhbGxSZXN1bHRzQW5hbHlzZXKCoWKU0f0bzQL0zQFAzLShY8KlZWRnZXOKpmJ2c3B2
   * b4OiY3CRgqF4y0Ba/pAyJ7TEoXnLQFegAAAAAAChbISheM0BAqF5zQcJpXdpZHRoXaZoZWlnaHQSoXCUks0BEs0HRJLNAQ/NB2CSzQEKzQeAks0BBs0HnacxcWxtNTZzg6JjcJGCoXjLQFrBb83YSzyhectAXiAAAAAAAKFshKF4zQEEoXnNBvmld2lkdGhopmhlaWdodBKhcJSSzQEFzQenks0BCc0Hi5LNAQ3NB2uSzQERzQdOpnlvZzN3c4KiY3CRgqF40f27oXnLQIPiZmZmZmahcJSSzLvNA/ySzLfNBBmSzLLNBDmSzK7NBFanMXhodDA0d4OiY3CRgqF4y8CCAeDVtFAkoXnLQHIYAAAAAAChbISheNH9v6F5zQFDpXdpZHRozPSmaGVpZ2h0EqFwl5LMmsy0kmLNATSSD80CJ5JMzQLuklXNAwuSZM0DJ5J1zQNApzFndHVuc3ODomNwkYKheMtAWQ32sN9rDqF5zQFMoWyE
   * oXjMkaF5zQcXpXdpZHRoXaZoZWlnaHQSoXCaksylzQenksyRzQeMkn/NB26SdM0HTpJZzQb6klzNBtySdM0GhpJ8zQZqksyKzQZOksyZzQY1pzFjaWg4MW+DomNwkYKheMtAWPIJTyCU8qF5zQFcoWyEoXjMj6F5zQcTpXdpZHRozImmaGVpZ2h0EqFwmpLMn80GLJLMjc0GR5J9zQZnknTNBoaSXM0G3JJZzQb6knTNB06Sfs0HapLMjs0HhpLMn80Hn6cxdWVlaGZzgqJjcJGCoXjLwGRWZmZmZmahectAg+OoBClRE6FwlJLM580C5JLM4s0DAZLM3c0DIZLM2M0DPqcxZTEyYXM5gqJjcJGCoXjLQHZWZmZmZmahectAhvBtW/YO6qFwlJLNAQXNAjCSzQEKzQITks0BD80B85LNARTNAdanMXY5MmM1MYOiY3CRgqF4y0BZ0aJwtrMEoXnLQIMEzMzMzM2hbISheGahec0CTaV3
   * aWR0aBmmaGVpZ2h0EqFwmpLNAUPNAuSSzQFWzQL/ks0BZ80DHpLNAXHNAz6SzQGwzQQLks0Bj80EU5LNAUrNBR6SzQFBzQU6ks0BM80FVpLNASTNBW+nMXBta3h1ZoOiY3CRgqF4y8BlTMzMzMzMoXnLQH079vAkP2+hbISheNH/DqF5zQHZpXdpZHRozJumaGVpZ2h0EqFwmpLNASPNBXiSzQEzzQVdks0BQs0FPZLNAUrNBR6SzQFhzQTIks0BZs0Eq5LNAUrNBFaSzQFBzQQ5ks0BMc0EHZLNAR/NBAQ=
   */
  view index {
    title 'Landscape view'
    style * {
      opacity 25%
    }
    include *
  	autoLayout LeftRight
	}

  dynamic view voice_pipeline_view1 {
    title "Voice Pipeline Agent Flow"
    // Include the pipeline components from within the Agent_system in Callevate
    include Callevate.Agent_system.VAD, 
            Callevate.Agent_system.EOU, 
            Callevate.Agent_system.STT_plug, 
            Callevate.Agent_system.LLM_plug, 
            Callevate.Agent_system.TTS_plug

    // Define the relationships to illustrate the sequential processing flow:
    Callevate.Agent_system.VAD -> Callevate.Agent_system.EOU "Detects & refines end-of-utterance"
    Callevate.Agent_system.EOU -> Callevate.Agent_system.STT_plug "Passes audio for transcription"
    Callevate.Agent_system.STT_plug -> Callevate.Agent_system.LLM_plug "Converts speech to text & processes input"
    Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.TTS_plug "Generates response & converts to speech"
  }

  dynamic view voice_pipeline_full_view  {
    title 'Full Voice Pipeline Agent Flow with RT-room-server Interaction'
    // Include key components of the voice pipeline and the RT-room-server interaction
    include Callevate.Agent_system, 
            Callevate.Agent_system.VAD, 
            Callevate.Agent_system.EOU, 
            Callevate.Agent_system.STT_plug, 
            Callevate.Agent_system.LLM_plug, 
            Callevate.Agent_system.TTS_plug,
            RT_room_server.RoomService

    // The VoicePipelineAgent (represented by Agent_system) connects to the room
    Callevate.Agent_system -> RT_room_server.RoomService "Joins room & publishes audio track"

    // Internal voice pipeline flow:
    Callevate.Agent_system.VAD -> Callevate.Agent_system.EOU "Detects & refines end-of-utterance"
    Callevate.Agent_system.EOU -> Callevate.Agent_system.STT_plug "Passes audio for transcription"
    Callevate.Agent_system.STT_plug -> Callevate.Agent_system.LLM_plug "Converts speech to text & processes input"
    Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.TTS_plug "Generates response & converts to speech"
    
    autoLayout LeftRight 150 120
  }
  dynamic view voice_pipeline_advanced  {
    // Speech detection and end-of-utterance
    title "Advanced Voice Pipeline Flow with Interruptions & Function Handling"
    Callevate.Agent_system.VAD -> Callevate.Agent_system.EOU "Detects speech boundaries & signals start-of-speech"
    Callevate.Agent_system.EOU -> Callevate.Agent_system.STT_plug "Determines end-of-speech; commits transcript"
    
    // Transcription to language processing
    Callevate.Agent_system.STT_plug -> Callevate.Agent_system.LLM_plug "Converts audio to text & sends transcription"
    
    // LLM processing with potential function calls:
    Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.TTS_plug "Processes text & generates response"
    Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.AgentFunctions "Triggers function call requests"
    Callevate.Agent_system.AgentFunctions -> Callevate.Agent_system.LLM_plug "Returns function results for enhanced reply"
    
    // Interruption handling:
    Callevate.Agent_system.VAD -> Callevate.Agent_system.Agent_Workflow "Triggers interruption if new speech is detected"
    Callevate.Agent_system.EOU -> Callevate.Agent_system.Agent_Workflow "Triggers next round when end of speech detected"
    // Final speech synthesis and publishing:
    Callevate.Agent_system.TTS_plug -> Callevate.Agent_system.Agent_Workflow "Synthesizes and plays agent speech"
    Callevate.Agent_system -> RT_room_server.RoomService "Joins room & publishes synthesized audio"
    
    autoLayout lr 150 120
  }

  /**
 * @likec4-generated(v1)
 * iKRoYXNo2Sg1ZWEzOTgxODQ0ZTZjZDhmNDk5MWU2ZTBiOTIzZmQ2NGQ4NTI3MDAwqmF1dG9MYXlvdXSDqWRpcmVjdGlvbqJCVKdub2RlU2VweKdyYW5rU2VwzJaheMyWoXnR/rKld2lkdGjNC5emaGVpZ2h0zQk/pW5vZGVzjLpSVF9yb29tX3NlcnZlci5Sb29tU2VydmljZYKhYpTNAzrR/rLNAUDMtKFjwqlDYWxsZXZhdGWCoWKUzKz3zQjOzQUyoWPDs0NhbGxSZXN1bHRzQW5hbHlzZXKCoWKUzQU50f6yzQFAzLShY8K2Q2FsbGV2YXRlLkFnZW50X3N5c3RlbYKhYpTM1C7NCH7NBNOhY8O6Q2FsbGV2YXRlLkFnZW50X3N5c3RlbS5WQUSCoWKUzQRMzI/NAUDMtKFjwrpDYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkVPVYKhYpTM/M0Bvc0BQMy0oWPCv0NhbGxldmF0ZS5B
 * Z2VudF9zeXN0ZW0uU1RUX3BsdWeCoWKUzQI6zIDNAUDMtKFjwr9DYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkxMTV9wbHVngqFilM0DQs0EJc0BQMy0oWPCv0NhbGxldmF0ZS5BZ2VudF9zeXN0ZW0uVFRTX3BsdWeCoWKUzQfqzQHdzQFAzLShY8LZJUNhbGxldmF0ZS5BZ2VudF9zeXN0ZW0uQWdlbnRGdW5jdGlvbnOCoWKUzQOMzQHpzQGQzQEToWPD2SVDYWxsZXZhdGUuQWdlbnRfc3lzdGVtLkFnZW50X1dvcmtmbG93gqFilM0H5GXNAUDMtKFjwtk2Q2FsbGV2YXRlLkFnZW50X3N5c3RlbS5BZ2VudEZ1bmN0aW9ucy5TZW50aW1lbnRzTW9kdWxlgqFilM0DtM0CIM0BQMy0oWPCpWVkZ2VzjqdzdGVwLTAxg6JjcJGCoXjLQIOuZmZmZmahecvAbZAAAAAAAKFshKF4zQgS
 * oXnNBpGld2lkdGjM2aZoZWlnaHQToXCUks0JRc0HupLNCSTNB5eSzQj8zQdvks0I1c0HR6dzdGVwLTAyg6JjcJGCoXjLQIpszMzMzM2hectAbTMzMzMzM6FshKF4zQGxoXnNA1ild2lkdGjNAQmmaGVpZ2h0IqFwlJLNB5DNBnWSzQZzzQYuks0EIs0FmpLNAv7NBVGnc3RlcC0wM4OiY3CRgqF4y0B1tmZmZmZmoXnLQHXeZmZmZmahbISheM0BlaF5zQE8pXdpZHRozPumaGVpZ2h0I6FwlJLNAlTNBMySzQJUzQSVks0CVM0ETJLNAlTNBBOnc3RlcC0wNIOiY3CRgqF4y0CFWzMzMzMzoXnLQG+ZmZmZmZqhbISheM0CK6F5zQLLpXdpZHRozPqmaGVpZ2h0I6FwmpLNAi7NA1WSzQIizQMsks0CH80C/ZLNAjvNAtySzQKDzQKGks0Czc0C3ZLNAzTNArGSzQNFzQKqks0DVc0C
 * oJLNA2XNApanc3RlcC0wNYOiY3CRgqF4y0CMjmZmZmZmoXnLQIXYzMzMzM2hbISheM0E3qF5zQLOpXdpZHRozQEXpmhlaWdodCOhcJqSzQRHzQKRks0EWc0CnJLNBGrNAqeSzQR8zQKxks0Ers0Cy5LNBMjNAriSzQTzzQLcks0FF80C+ZLNBTPNAySSzQVHzQNLp3N0ZXAtMDaDomNwkYKheMtAkp5mZmZmZqF5y0CLrmZmZmZmoWyEoXjNBy+hec0DOKV3aWR0aMz9pmhlaWdodCOhcJeSzQYPzQOQks0GgM0Dd5LNBxnNA0ySzQeTzQMJks0Hys0C65LNCADNAsCSzQgszQKYp3N0ZXAtMDeDomNwkYKheMtAisGZmZmZmqF5y0CLyzMzMzMzoWyEoXjNBBKhec0DWqV3aWR0aM0BDaZoZWlnaHQToXCXks0Ez80DnpLNBG7NA4uSzQP5zQNjks0Du80DCZLNA6vNAvKSzQOnzQLX
 * ks0Dqs0Cu6dzdGVwLTA4g6JjcJGCoXjLQIuuZmZmZmahectAhKTMzMzMzaFshKF4zQSAoXnNA5ald2lkdGjM+aZoZWlnaHQjoXCXks0EfM0CsZLNBOHNAteSzQYVzQKMks0GXs0C3JLNBovNAw2SzQZZzQM+ks0GGM0DZKdzdGVwLTA5g6JjcJGCoXjNBWChectAYEzMzMzMzaFshKF4zQfNoXnNBYeld2lkdGjM+KZoZWlnaHQjoXDcABOSzQg+zQZEks0IUc0F0ZLNCHbNBQGSzQikzQRTks0Iw80D4JLNCMXNA8CSzQj5zQNUks0JIM0DBpLNCUXNAwKSzQlmzQKxks0Jl80COZLNCZLNAhOSzQmezQGSks0JoM0BfpLNCajNAXaSzQmezQFkks0JhM0BOJLNCVrNARaSzQktzP2nc3RlcC0xMIOiY3CRgqF4y0CIczMzMzMzoXnLQHVczMzMzM2hbISheM0FEKF5zQFrpXdpZHRo
 * zQECpmhlaWdodCOhcJqSzQHfzQTMks0BV80EWpLMls0DjZLM+s0C3JLNAX3NAfWSzQIBzQIDks0C+s0BqJLNBKnNAQmSzQbKzNeSzQfazMenc3RlcC0xMYKhbISheM0IaqF5zQGApXdpZHRozQEMpmhlaWdodCOhcJeSzQh2zQHdks0Ics0BxZLNCG3NAaqSzQhrzQGSks0IaM0BbpLNCGvNAUaSzQhwzQEjp3N0ZXAtMTKDomNwkYKheMtAeMMzMzMzM6F50LyhbISheM0Dp6F5zQJ9pXdpZHRozOumaGVpZ2h0I6FwmpLNCczNAXKSzQtizQMjks0Lds0D95LNC/bNBkSSzQwOzQaxks0MLc0G3pLNC/bNB0CSzQvKzQeNks0K6M0HzJLNCkbNB/Gnc3RlcC0xM4OiY3CRgqF4y0COmmZmZmZmoXnLwHOXAquvHiShbISheM0ItqF5zQcWpXdpZHRozQEXpmhlaWdodBOhcJSSzQn3
 * zQe6ks0KMc0HgZLNCnzNBzmSzQq3zQb/p3N0ZXAtMTSDomNwkYKheMtAjzwAAAAAAKF5y8BgdyjXKNcqoWyEoXjNBiShec0C66V3aWR0aM0BJaZoZWlnaHQjoXCUks0Kes0GnpLNCknNBp6SzQoRzQaeks0J1s0Gng==
 */
dynamic view voice_pipeline_advanced1 {
  title "Advanced Voice Pipeline Flow with Stages, Interruptions, Function Handling, Sentiment Analysis & Results Analysis"
  
  RT_room_server.RoomService -> Callevate "Stage 0: Subscriber connected"
  // Stage 1: Input Detection & Transcription
  // The agent listens to incoming speech and commits the transcript.
  Callevate.Agent_system.VAD -> Callevate.Agent_system.EOU "Stage 1: Detects speech boundaries & signals start-of-speech"
  Callevate.Agent_system.EOU -> Callevate.Agent_system.STT_plug "Stage 1: Determines end-of-speech; commits transcript"
  
  // Stage 2: Sentiment Analysis & Processing
  // The transcribed text is first sent to the Sentiments Module for analysis, then forwarded to the LLM.
  Callevate.Agent_system.STT_plug -> Callevate.Agent_system.AgentFunctions.SentimentsModule "Stage 2: Analyzes sentiment of user input"
  Callevate.Agent_system.AgentFunctions.SentimentsModule -> Callevate.Agent_system.LLM_plug "Stage 2: Provides sentiment context with transcription"
  
  // Additional processing and function call handling
  Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.TTS_plug "Stage 2: Processes text & generates preliminary response"
  Callevate.Agent_system.LLM_plug -> Callevate.Agent_system.AgentFunctions "Stage 2: Triggers function call requests"
  Callevate.Agent_system.AgentFunctions -> Callevate.Agent_system.LLM_plug "Stage 2: Returns function results for enhanced reply"
  
  // Stage 3: Interruption, Synthesis & Publishing
  // The agent handles interruptions, synthesizes the final response, and publishes audio.
  Callevate.Agent_system.VAD -> Callevate.Agent_system.Agent_Workflow "Stage 3: Triggers interruption if new speech is detected"
  Callevate.Agent_system.EOU -> Callevate.Agent_system.Agent_Workflow "Stage 3: Signals readiness for next round when end-of-speech detected"
  Callevate.Agent_system.TTS_plug -> Callevate.Agent_system.Agent_Workflow "Stage 3: Synthesizes and plays agent speech"
  Callevate.Agent_system -> RT_room_server.RoomService "Stage 3: Joins room & publishes synthesized audio"
  
  // Stage 4: Results Analysis
  // Once the call is finished, the room service forwards call data to the Call Results Analyser,
  // and analysis feedback is integrated back into the agent.
  RT_room_server.RoomService -> CallResultsAnalyser "Stage 4: Forwards call data for analysis"
  CallResultsAnalyser -> Callevate.Agent_system "Stage 4: Feeds analysis feedback back to the agent"
  
  autoLayout BottomTop 150 120
  style Callevate.Agent_system {
    border dashed
  }
}
}