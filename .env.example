# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-instance.livekit.cloud  # ws://localhost:7880
LIVEKIT_API_KEY=your_livekit_api_key
LIVEKIT_API_SECRET=your_livekit_api_secret

# Langfuse Configuration
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
LANGFUSE_SECRET_KEY=your_langfuse_secret_key
LANGFUSE_HOST=https://cloud.langfuse.com  # Optional: Use this for EU region or self-hosted
# LANGFUSE_HOST=https://us.cloud.langfuse.com  # Uncomment for US region

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
LLM_MODEL=gpt-4o  # Or another model like gpt-3.5-turbo

# Deepgram Configuration
DEEPGRAM_API_KEY=your_deepgram_api_key
DEEPGRAM_MODEL=nova-3

# Voice Assistant Configuration
VOICE_ACTIVITY_TIMEOUT=5  # Timeout in seconds for voice activity detection
MAX_RESPONSE_TOKENS=150  # Maximum number of tokens for assistant responses

# Logging Configuration
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL

# Optional: Sentry Configuration (for error tracking)
# SENTRY_DSN=your_sentry_dsn
# SENTRY_ENVIRONMENT=environment

# Tavily Configuration
TAVILY_API_KEY=your_tavily_api_key

# TTS PROVIDERS
VOICE_PROVIDER=11labs # Options: 11labs , playai

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key

#PlayAi Voice
PLAYHT_USER_ID=_user_id
PLAYHT_API_KEY=_api_key

# STT Configuration
STT_PLUGIN=deepgram  # Options: deepgram, assemblyai

# AssemblyAI Configuration (if using AssemblyAI)
ASSEMBLYAI_API_KEY=your_assemblyai_api_key
ASSEMBLYAI_MODEL=default

